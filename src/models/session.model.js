/**
 * <PERSON>lo sessione per MongoDB
 */
import mongoose from 'mongoose';

/**
 * Schema sessione
 */
const sessionSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  partnerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    index: true
  },
  startTime: {
    type: Date,
    default: Date.now
  },
  endTime: {
    type: Date
  },
  duration: {
    type: Number,
    default: 0
  },
  creditsUsed: {
    type: Number,
    default: 0
  },
  status: {
    type: String,
    enum: ['active', 'ended', 'interrupted'],
    default: 'active'
  },
  userRating: {
    type: Number,
    min: 1,
    max: 5
  },
  partnerRating: {
    type: Number,
    min: 1,
    max: 5
  },
  userFeedback: String,
  partnerFeedback: String,
  userReported: {
    type: Boolean,
    default: false
  },
  partnerReported: {
    type: Boolean,
    default: false
  },
  userReportReason: String,
  partnerReportReason: String,
  ipAddress: String,
  userAgent: String,
  deviceInfo: {
    type: Map,
    of: String
  },
  location: {
    country: String,
    city: String,
    coordinates: {
      type: [Number],
      index: '2dsphere'
    }
  },
  connectionQuality: {
    type: String,
    enum: ['excellent', 'good', 'fair', 'poor'],
    default: 'good'
  },
  events: [{
    type: {
      type: String,
      enum: ['connect', 'disconnect', 'message', 'report', 'rating', 'credit_use']
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    data: mongoose.Schema.Types.Mixed
  }]
}, {
  timestamps: true
});

// Indici per migliorare le performance delle query
sessionSchema.index({ startTime: -1 });
sessionSchema.index({ status: 1 });
sessionSchema.index({ 'location.country': 1 });

/**
 * Metodi pre-save
 */
sessionSchema.pre('save', function(next) {
  // Calcola la durata se la sessione è terminata
  if (this.endTime && !this.duration) {
    this.duration = Math.round((this.endTime - this.startTime) / 1000);
  }
  next();
});

/**
 * Metodi dell'istanza
 */

/**
 * Termina la sessione
 * @param {Object} options - Opzioni per la terminazione
 * @returns {Promise<Session>} Promise che si risolve con la sessione aggiornata
 */
sessionSchema.methods.endSession = async function(options = {}) {
  this.endTime = options.endTime || new Date();
  this.status = options.status || 'ended';
  this.duration = Math.round((this.endTime - this.startTime) / 1000);

  if (options.userRating) {
    this.userRating = options.userRating;
  }

  if (options.partnerRating) {
    this.partnerRating = options.partnerRating;
  }

  if (options.userFeedback) {
    this.userFeedback = options.userFeedback;
  }

  if (options.partnerFeedback) {
    this.partnerFeedback = options.partnerFeedback;
  }

  // Aggiungi evento di disconnessione
  this.events.push({
    type: 'disconnect',
    timestamp: this.endTime,
    data: { reason: options.reason }
  });

  return this.save();
};

/**
 * Aggiunge un evento alla sessione
 * @param {string} type - Tipo di evento
 * @param {Object} data - Dati dell'evento
 * @returns {Promise<Session>} Promise che si risolve con la sessione aggiornata
 */
sessionSchema.methods.addEvent = async function(type, data = {}) {
  this.events.push({
    type,
    timestamp: new Date(),
    data
  });

  return this.save();
};

/**
 * Segnala un utente
 * @param {string} reporter - 'user' o 'partner'
 * @param {string} reason - Motivo della segnalazione
 * @returns {Promise<Session>} Promise che si risolve con la sessione aggiornata
 */
sessionSchema.methods.reportUser = async function(reporter, reason) {
  if (reporter === 'user') {
    this.partnerReported = true;
    this.partnerReportReason = reason;
  } else if (reporter === 'partner') {
    this.userReported = true;
    this.userReportReason = reason;
  }

  // Aggiungi evento di segnalazione
  this.events.push({
    type: 'report',
    data: { reporter, reason }
  });

  return this.save();
};

/**
 * Metodi statici
 */

/**
 * Trova le sessioni attive di un utente
 * @param {string} userId - ID dell'utente
 * @returns {Promise<Array>} Promise che si risolve con le sessioni attive
 */
sessionSchema.statics.findActiveByUser = function(userId) {
  return this.find({
    userId,
    status: 'active'
  });
};

/**
 * Trova le sessioni tra due utenti
 * @param {string} userId - ID del primo utente
 * @param {string} partnerId - ID del secondo utente
 * @returns {Promise<Array>} Promise che si risolve con le sessioni trovate
 */
sessionSchema.statics.findBetweenUsers = function(userId, partnerId) {
  return this.find({
    $or: [
      { userId, partnerId },
      { userId: partnerId, partnerId: userId }
    ]
  }).sort({ startTime: -1 });
};

/**
 * Calcola le statistiche delle sessioni di un utente
 * @param {string} userId - ID dell'utente
 * @returns {Promise<Object>} Promise che si risolve con le statistiche
 */
sessionSchema.statics.getUserStats = async function(userId) {
  const stats = await this.aggregate([
    { $match: { userId: mongoose.Types.ObjectId(userId) } },
    { $group: {
      _id: null,
      totalSessions: { $sum: 1 },
      totalDuration: { $sum: '$duration' },
      totalCreditsUsed: { $sum: '$creditsUsed' },
      avgRating: { $avg: '$userRating' }
    }}
  ]);

  return stats.length > 0 ? stats[0] : {
    totalSessions: 0,
    totalDuration: 0,
    totalCreditsUsed: 0,
    avgRating: 0
  };
};

// Crea e esporta il modello
const Session = mongoose.model('Session', sessionSchema);
export default Session;
