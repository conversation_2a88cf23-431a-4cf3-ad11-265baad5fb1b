const mongoose = require('mongoose');

const reportSchema = new mongoose.Schema({
  // Chi ha fatto la segnalazione
  reporterId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  // Chi è stato segnalato
  reportedUserId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  // Motivo della segnalazione
  reason: {
    type: String,
    required: true,
    enum: [
      'inappropriate_content',
      'offensive_behavior', 
      'spam',
      'harassment',
      'fake_profile',
      'underage',
      'other'
    ]
  },
  
  // Descrizione dettagliata (opzionale)
  description: {
    type: String,
    maxlength: 500,
    default: ''
  },
  
  // ID della chat/sessione dove è avvenuto l'incidente
  chatId: {
    type: String,
    default: null
  },
  
  // Stato della segnalazione
  status: {
    type: String,
    enum: ['pending', 'reviewing', 'resolved', 'dismissed'],
    default: 'pending'
  },
  
  // Note dell'amministratore
  adminNotes: {
    type: String,
    default: ''
  },
  
  // Chi ha gestito la segnalazione
  reviewedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  
  // Azione intrapresa
  actionTaken: {
    type: String,
    enum: ['none', 'warning', 'temporary_ban', 'permanent_ban', 'account_deletion'],
    default: 'none'
  },
  
  // Priorità della segnalazione
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },
  
  // Categoria automatica basata sul contenuto
  category: {
    type: String,
    enum: ['content', 'behavior', 'technical', 'safety'],
    default: 'behavior'
  },
  
  // Metadati aggiuntivi
  metadata: {
    userAgent: String,
    ipAddress: String,
    timestamp: Date,
    sessionDuration: Number,
    reportSource: {
      type: String,
      enum: ['chat', 'video', 'profile', 'other'],
      default: 'chat'
    }
  }
}, {
  timestamps: true,
  collection: 'reports'
});

// Indici per performance
reportSchema.index({ reporterId: 1 });
reportSchema.index({ reportedUserId: 1 });
reportSchema.index({ status: 1 });
reportSchema.index({ createdAt: -1 });
reportSchema.index({ priority: 1, status: 1 });

// Metodi del modello
reportSchema.statics.getReportsByUser = function(userId) {
  return this.find({ reportedUserId: userId })
    .populate('reporterId', 'email')
    .sort({ createdAt: -1 });
};

reportSchema.statics.getPendingReports = function() {
  return this.find({ status: 'pending' })
    .populate('reporterId reportedUserId', 'email')
    .sort({ priority: -1, createdAt: -1 });
};

reportSchema.statics.getReportStats = function() {
  return this.aggregate([
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 }
      }
    }
  ]);
};

// Middleware pre-save per categorizzazione automatica
reportSchema.pre('save', function(next) {
  // Categorizza automaticamente in base al motivo
  switch (this.reason) {
    case 'inappropriate_content':
    case 'spam':
      this.category = 'content';
      break;
    case 'offensive_behavior':
    case 'harassment':
      this.category = 'behavior';
      break;
    case 'underage':
    case 'fake_profile':
      this.category = 'safety';
      this.priority = 'high';
      break;
    default:
      this.category = 'behavior';
  }
  
  // Imposta priorità alta per motivi gravi
  if (['underage', 'harassment'].includes(this.reason)) {
    this.priority = 'high';
  }
  
  next();
});

// Metodi di istanza
reportSchema.methods.resolve = function(adminId, action, notes) {
  this.status = 'resolved';
  this.reviewedBy = adminId;
  this.actionTaken = action;
  this.adminNotes = notes;
  return this.save();
};

reportSchema.methods.dismiss = function(adminId, reason) {
  this.status = 'dismissed';
  this.reviewedBy = adminId;
  this.adminNotes = reason;
  return this.save();
};

// Virtual per ottenere il tempo trascorso dalla segnalazione
reportSchema.virtual('timeElapsed').get(function() {
  return Date.now() - this.createdAt.getTime();
});

// Trasformazione JSON per nascondere campi sensibili
reportSchema.methods.toJSON = function() {
  const report = this.toObject();
  
  // Nascondi informazioni sensibili se non admin
  if (!this.isAdminView) {
    delete report.metadata?.ipAddress;
    delete report.adminNotes;
  }
  
  return report;
};

module.exports = mongoose.model('Report', reportSchema);
