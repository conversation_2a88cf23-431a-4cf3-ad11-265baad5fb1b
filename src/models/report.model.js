/**
 * <PERSON>lo report per MongoDB
 */
import mongoose from 'mongoose';

/**
 * Schema report
 */
const reportSchema = new mongoose.Schema({
  reporterId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  reportedId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  sessionId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Session',
    required: true,
    index: true
  },
  reason: {
    type: String,
    enum: [
      'inappropriate_content',
      'harassment',
      'spam',
      'underage',
      'illegal_activity',
      'hate_speech',
      'violence',
      'other'
    ],
    required: true
  },
  description: {
    type: String
  },
  status: {
    type: String,
    enum: ['pending', 'reviewed', 'resolved', 'dismissed'],
    default: 'pending',
    index: true
  },
  reviewerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    sparse: true
  },
  reviewNotes: {
    type: String
  },
  actionTaken: {
    type: String,
    enum: ['none', 'warning', 'temporary_ban', 'permanent_ban'],
    default: 'none'
  },
  evidence: {
    type: [String],
    default: []
  },
  ipAddress: String,
  userAgent: String
}, {
  timestamps: true
});

// Indici per migliorare le performance delle query
reportSchema.index({ createdAt: -1 });
reportSchema.index({ reporterId: 1, reportedId: 1 });
reportSchema.index({ status: 1, createdAt: 1 });

/**
 * Metodi dell'istanza
 */

/**
 * Aggiorna lo stato del report
 * @param {string} status - Nuovo stato
 * @param {Object} options - Opzioni aggiuntive
 * @returns {Promise<Report>} Promise che si risolve con il report aggiornato
 */
reportSchema.methods.updateStatus = async function(status, options = {}) {
  this.status = status;

  if (options.reviewerId) {
    this.reviewerId = options.reviewerId;
  }

  if (options.reviewNotes) {
    this.reviewNotes = options.reviewNotes;
  }

  if (options.actionTaken) {
    this.actionTaken = options.actionTaken;
  }

  return this.save();
};

/**
 * Aggiunge prove al report
 * @param {string|Array} evidence - Prova o array di prove
 * @returns {Promise<Report>} Promise che si risolve con il report aggiornato
 */
reportSchema.methods.addEvidence = async function(evidence) {
  if (Array.isArray(evidence)) {
    this.evidence = [...this.evidence, ...evidence];
  } else {
    this.evidence.push(evidence);
  }

  return this.save();
};

/**
 * Metodi statici
 */

/**
 * Trova i report pendenti
 * @param {Object} options - Opzioni di ricerca
 * @returns {Promise<Array>} Promise che si risolve con i report trovati
 */
reportSchema.statics.findPending = function(options = {}) {
  const query = { status: 'pending' };

  if (options.reason) {
    query.reason = options.reason;
  }

  return this.find(query)
    .sort({ createdAt: -1 })
    .limit(options.limit || 100)
    .populate('reporterId', 'email')
    .populate('reportedId', 'email')
    .populate('sessionId');
};

/**
 * Trova i report per un utente segnalato
 * @param {string} userId - ID dell'utente segnalato
 * @returns {Promise<Array>} Promise che si risolve con i report trovati
 */
reportSchema.statics.findByReportedUser = function(userId) {
  return this.find({ reportedId: userId })
    .sort({ createdAt: -1 })
    .populate('reporterId', 'email')
    .populate('sessionId');
};

/**
 * Trova i report fatti da un utente
 * @param {string} userId - ID dell'utente che ha fatto le segnalazioni
 * @returns {Promise<Array>} Promise che si risolve con i report trovati
 */
reportSchema.statics.findByReporter = function(userId) {
  return this.find({ reporterId: userId })
    .sort({ createdAt: -1 })
    .populate('reportedId', 'email')
    .populate('sessionId');
};

/**
 * Conta i report per un utente segnalato
 * @param {string} userId - ID dell'utente segnalato
 * @returns {Promise<Object>} Promise che si risolve con il conteggio dei report
 */
reportSchema.statics.countByReportedUser = async function(userId) {
  const counts = await this.aggregate([
    { $match: { reportedId: mongoose.Types.ObjectId(userId) } },
    { $group: {
      _id: '$reason',
      count: { $sum: 1 }
    }}
  ]);

  const result = {
    total: 0
  };

  counts.forEach(item => {
    result[item._id] = item.count;
    result.total += item.count;
  });

  return result;
};

/**
 * Verifica se un utente è stato segnalato recentemente
 * @param {string} userId - ID dell'utente da verificare
 * @param {number} days - Numero di giorni da considerare
 * @returns {Promise<boolean>} Promise che si risolve con true se l'utente è stato segnalato
 */
reportSchema.statics.hasRecentReports = async function(userId, days = 7) {
  const date = new Date();
  date.setDate(date.getDate() - days);

  const count = await this.countDocuments({
    reportedId: userId,
    createdAt: { $gte: date }
  });

  return count > 0;
};

// Crea e esporta il modello
const Report = mongoose.model('Report', reportSchema);
export default Report;
