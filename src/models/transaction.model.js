/**
 * Modello transazione per MongoDB
 */
import mongoose from 'mongoose';

/**
 * Schema transazione
 */
const transactionSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  type: {
    type: String,
    enum: ['purchase', 'usage', 'refund', 'bonus', 'gift'],
    required: true,
    index: true
  },
  amount: {
    type: Number,
    required: true
  },
  credits: {
    type: Number,
    required: true
  },
  currency: {
    type: String,
    default: 'EUR'
  },
  status: {
    type: String,
    enum: ['pending', 'completed', 'failed', 'refunded'],
    default: 'pending',
    index: true
  },
  paymentMethod: {
    type: String,
    enum: ['credit_card', 'paypal', 'bank_transfer', 'crypto', 'system'],
    default: 'system'
  },
  paymentId: {
    type: String,
    sparse: true
  },
  description: {
    type: String
  },
  metadata: {
    type: Map,
    of: mongoose.Schema.Types.Mixed
  },
  relatedSessionId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Session',
    sparse: true
  },
  relatedTransactionId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Transaction',
    sparse: true
  },
  ipAddress: String,
  userAgent: String
}, {
  timestamps: true
});

// Indici per migliorare le performance delle query
transactionSchema.index({ createdAt: -1 });
transactionSchema.index({ userId: 1, type: 1 });
transactionSchema.index({ userId: 1, status: 1 });

/**
 * Metodi dell'istanza
 */

/**
 * Completa la transazione
 * @param {Object} options - Opzioni per il completamento
 * @returns {Promise<Transaction>} Promise che si risolve con la transazione aggiornata
 */
transactionSchema.methods.complete = async function(options = {}) {
  this.status = 'completed';

  if (options.paymentId) {
    this.paymentId = options.paymentId;
  }

  if (options.metadata) {
    this.metadata = { ...this.metadata, ...options.metadata };
  }

  // Aggiorna i crediti dell'utente
  const User = mongoose.model('User');
  const user = await User.findById(this.userId);

  if (user) {
    if (this.type === 'purchase' || this.type === 'bonus' || this.type === 'gift') {
      user.credits += this.credits;
    } else if (this.type === 'usage') {
      user.credits -= this.credits;
    } else if (this.type === 'refund') {
      user.credits += this.credits;
    }

    await user.save();
  }

  return this.save();
};

/**
 * Fallisce la transazione
 * @param {Object} options - Opzioni per il fallimento
 * @returns {Promise<Transaction>} Promise che si risolve con la transazione aggiornata
 */
transactionSchema.methods.fail = async function(options = {}) {
  this.status = 'failed';

  if (options.description) {
    this.description = options.description;
  }

  if (options.metadata) {
    this.metadata = { ...this.metadata, ...options.metadata };
  }

  return this.save();
};

/**
 * Rimborsa la transazione
 * @param {Object} options - Opzioni per il rimborso
 * @returns {Promise<Transaction>} Promise che si risolve con la transazione aggiornata
 */
transactionSchema.methods.refund = async function(options = {}) {
  if (this.status !== 'completed') {
    throw new Error('Solo le transazioni completate possono essere rimborsate');
  }

  this.status = 'refunded';

  if (options.description) {
    this.description = options.description;
  }

  if (options.metadata) {
    this.metadata = { ...this.metadata, ...options.metadata };
  }

  // Crea una nuova transazione di rimborso
  const Transaction = mongoose.model('Transaction');
  const refundTransaction = new Transaction({
    userId: this.userId,
    type: 'refund',
    amount: this.amount,
    credits: this.credits,
    currency: this.currency,
    status: 'completed',
    paymentMethod: this.paymentMethod,
    description: `Rimborso per transazione ${this._id}`,
    relatedTransactionId: this._id,
    ipAddress: options.ipAddress || this.ipAddress,
    userAgent: options.userAgent || this.userAgent
  });

  await refundTransaction.save();

  // Aggiorna i crediti dell'utente
  const User = mongoose.model('User');
  const user = await User.findById(this.userId);

  if (user) {
    if (this.type === 'purchase' || this.type === 'bonus' || this.type === 'gift') {
      user.credits -= this.credits;
    } else if (this.type === 'usage') {
      user.credits += this.credits;
    }

    await user.save();
  }

  return this.save();
};

/**
 * Metodi statici
 */

/**
 * Crea una nuova transazione di acquisto
 * @param {Object} options - Opzioni per la transazione
 * @returns {Promise<Transaction>} Promise che si risolve con la transazione creata
 */
transactionSchema.statics.createPurchase = async function(options) {
  const transaction = new this({
    userId: options.userId,
    type: 'purchase',
    amount: options.amount,
    credits: options.credits,
    currency: options.currency || 'EUR',
    status: 'pending',
    paymentMethod: options.paymentMethod,
    description: options.description || `Acquisto di ${options.credits} crediti`,
    metadata: options.metadata,
    ipAddress: options.ipAddress,
    userAgent: options.userAgent
  });

  return transaction.save();
};

/**
 * Crea una nuova transazione di utilizzo
 * @param {Object} options - Opzioni per la transazione
 * @returns {Promise<Transaction>} Promise che si risolve con la transazione creata
 */
transactionSchema.statics.createUsage = async function(options) {
  const transaction = new this({
    userId: options.userId,
    type: 'usage',
    amount: 0,
    credits: options.credits,
    status: 'completed',
    paymentMethod: 'system',
    description: options.description || `Utilizzo di ${options.credits} crediti`,
    relatedSessionId: options.sessionId,
    ipAddress: options.ipAddress,
    userAgent: options.userAgent
  });

  // Aggiorna i crediti dell'utente
  const User = mongoose.model('User');
  const user = await User.findById(options.userId);

  if (user) {
    user.credits -= options.credits;
    await user.save();
  }

  return transaction.save();
};

/**
 * Ottiene il bilancio delle transazioni di un utente
 * @param {string} userId - ID dell'utente
 * @returns {Promise<Object>} Promise che si risolve con il bilancio
 */
transactionSchema.statics.getUserBalance = async function(userId) {
  const balance = await this.aggregate([
    { $match: { userId: mongoose.Types.ObjectId(userId), status: 'completed' } },
    { $group: {
      _id: '$type',
      total: { $sum: '$credits' }
    }}
  ]);

  let purchased = 0;
  let used = 0;
  let refunded = 0;
  let bonus = 0;
  let gift = 0;

  balance.forEach(item => {
    switch (item._id) {
      case 'purchase':
        purchased = item.total;
        break;
      case 'usage':
        used = item.total;
        break;
      case 'refund':
        refunded = item.total;
        break;
      case 'bonus':
        bonus = item.total;
        break;
      case 'gift':
        gift = item.total;
        break;
    }
  });

  return {
    purchased,
    used,
    refunded,
    bonus,
    gift,
    balance: purchased + refunded + bonus + gift - used
  };
};

// Crea e esporta il modello
const Transaction = mongoose.model('Transaction', transactionSchema);
export default Transaction;
