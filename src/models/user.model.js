/**
 * <PERSON>lo utente per MongoDB
 */
const mongoose = require('mongoose');
const bcrypt = require('bcrypt');

/**
 * Schema utente
 */
const userSchema = new mongoose.Schema({
  email: {
    type: String,
    required: true,
    unique: true,
    index: true,
    lowercase: true,
    trim: true,
    match: [/^\S+@\S+\.\S+$/, 'Formato email non valido']
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  // Sistema gratuito fino a 10.000 utenti
  freeUser: {
    type: Boolean,
    default: true
  },
  isOnline: {
    type: Boolean,
    default: false
  },
  lastLogin: {
    type: Date
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  // Campi per sistema di report e moderazione
  reportCount: {
    type: Number,
    default: 0
  },
  status: {
    type: String,
    enum: ['active', 'suspended', 'banned'],
    default: 'active'
  },
  suspendedAt: {
    type: Date,
    default: null
  },
  suspendedReason: {
    type: String,
    default: ''
  },
  blockedUsers: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  isAdmin: {
    type: Boolean,
    default: false
  },
  // Preferenze utente
  preferences: {
    gender: {
      type: String,
      enum: ['all', 'male', 'female'],
      default: 'all'
    },
    ageRange: {
      min: { type: Number, default: 18 },
      max: { type: Number, default: 99 }
    }
  },
  // Statistiche utente
  stats: {
    totalChats: { type: Number, default: 0 },
    totalMinutes: { type: Number, default: 0 },
    reportsReceived: { type: Number, default: 0 },
    reportsMade: { type: Number, default: 0 }
  }
});

// Compare password method (non pre-hash per compatibilità con server esistente)
userSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

// Metodo per validare email
userSchema.statics.isValidEmail = function(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) return false;

  const validDomains = [
    'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com',
    'icloud.com', 'live.com', 'msn.com', 'libero.it',
    'virgilio.it', 'alice.it', 'tin.it', 'fastwebnet.it'
  ];

  const domain = email.split('@')[1].toLowerCase();
  return validDomains.includes(domain);
};

module.exports = mongoose.model('User', userSchema);
