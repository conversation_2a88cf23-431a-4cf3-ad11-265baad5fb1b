/**
 * Utilità per la gestione delle date
 */

/**
 * Formatta una data in formato ISO
 * @param {Date|string|number} date - Data da formattare
 * @returns {string} Data formattata in formato ISO
 */
const formatISODate = (date) => {
  const d = date instanceof Date ? date : new Date(date);
  return d.toISOString();
};

/**
 * Formatta una data in formato locale
 * @param {Date|string|number} date - Data da formattare
 * @param {string} locale - Locale da utilizzare
 * @param {Object} options - Opzioni di formattazione
 * @returns {string} Data formattata in formato locale
 */
const formatLocalDate = (date, locale = 'it-IT', options = {}) => {
  const d = date instanceof Date ? date : new Date(date);
  const defaultOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  };
  
  return d.toLocaleDateString(locale, { ...defaultOptions, ...options });
};

/**
 * Formatta una data in formato locale con ora
 * @param {Date|string|number} date - Data da formattare
 * @param {string} locale - Locale da utilizzare
 * @param {Object} options - Opzioni di formattazione
 * @returns {string} Data formattata in formato locale con ora
 */
const formatLocalDateTime = (date, locale = 'it-IT', options = {}) => {
  const d = date instanceof Date ? date : new Date(date);
  const defaultOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  };
  
  return d.toLocaleString(locale, { ...defaultOptions, ...options });
};

/**
 * Formatta una data in formato relativo
 * @param {Date|string|number} date - Data da formattare
 * @param {string} locale - Locale da utilizzare
 * @returns {string} Data formattata in formato relativo
 */
const formatRelativeDate = (date, locale = 'it-IT') => {
  const d = date instanceof Date ? date : new Date(date);
  const now = new Date();
  const diffMs = now - d;
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);
  const diffMonth = Math.floor(diffDay / 30);
  const diffYear = Math.floor(diffMonth / 12);
  
  if (diffSec < 60) {
    return 'Adesso';
  } else if (diffMin < 60) {
    return `${diffMin} ${diffMin === 1 ? 'minuto' : 'minuti'} fa`;
  } else if (diffHour < 24) {
    return `${diffHour} ${diffHour === 1 ? 'ora' : 'ore'} fa`;
  } else if (diffDay < 30) {
    return `${diffDay} ${diffDay === 1 ? 'giorno' : 'giorni'} fa`;
  } else if (diffMonth < 12) {
    return `${diffMonth} ${diffMonth === 1 ? 'mese' : 'mesi'} fa`;
  } else {
    return `${diffYear} ${diffYear === 1 ? 'anno' : 'anni'} fa`;
  }
};

/**
 * Aggiunge un numero di millisecondi a una data
 * @param {Date|string|number} date - Data di partenza
 * @param {number} ms - Millisecondi da aggiungere
 * @returns {Date} Nuova data
 */
const addMilliseconds = (date, ms) => {
  const d = date instanceof Date ? new Date(date) : new Date(date);
  d.setTime(d.getTime() + ms);
  return d;
};

/**
 * Aggiunge un numero di secondi a una data
 * @param {Date|string|number} date - Data di partenza
 * @param {number} seconds - Secondi da aggiungere
 * @returns {Date} Nuova data
 */
const addSeconds = (date, seconds) => {
  return addMilliseconds(date, seconds * 1000);
};

/**
 * Aggiunge un numero di minuti a una data
 * @param {Date|string|number} date - Data di partenza
 * @param {number} minutes - Minuti da aggiungere
 * @returns {Date} Nuova data
 */
const addMinutes = (date, minutes) => {
  return addMilliseconds(date, minutes * 60 * 1000);
};

/**
 * Aggiunge un numero di ore a una data
 * @param {Date|string|number} date - Data di partenza
 * @param {number} hours - Ore da aggiungere
 * @returns {Date} Nuova data
 */
const addHours = (date, hours) => {
  return addMilliseconds(date, hours * 60 * 60 * 1000);
};

/**
 * Aggiunge un numero di giorni a una data
 * @param {Date|string|number} date - Data di partenza
 * @param {number} days - Giorni da aggiungere
 * @returns {Date} Nuova data
 */
const addDays = (date, days) => {
  return addMilliseconds(date, days * 24 * 60 * 60 * 1000);
};

/**
 * Aggiunge un numero di mesi a una data
 * @param {Date|string|number} date - Data di partenza
 * @param {number} months - Mesi da aggiungere
 * @returns {Date} Nuova data
 */
const addMonths = (date, months) => {
  const d = date instanceof Date ? new Date(date) : new Date(date);
  d.setMonth(d.getMonth() + months);
  return d;
};

/**
 * Aggiunge un numero di anni a una data
 * @param {Date|string|number} date - Data di partenza
 * @param {number} years - Anni da aggiungere
 * @returns {Date} Nuova data
 */
const addYears = (date, years) => {
  const d = date instanceof Date ? new Date(date) : new Date(date);
  d.setFullYear(d.getFullYear() + years);
  return d;
};

/**
 * Calcola la differenza in millisecondi tra due date
 * @param {Date|string|number} date1 - Prima data
 * @param {Date|string|number} date2 - Seconda data
 * @returns {number} Differenza in millisecondi
 */
const diffMilliseconds = (date1, date2) => {
  const d1 = date1 instanceof Date ? date1 : new Date(date1);
  const d2 = date2 instanceof Date ? date2 : new Date(date2);
  return d2 - d1;
};

/**
 * Calcola la differenza in secondi tra due date
 * @param {Date|string|number} date1 - Prima data
 * @param {Date|string|number} date2 - Seconda data
 * @returns {number} Differenza in secondi
 */
const diffSeconds = (date1, date2) => {
  return Math.floor(diffMilliseconds(date1, date2) / 1000);
};

/**
 * Calcola la differenza in minuti tra due date
 * @param {Date|string|number} date1 - Prima data
 * @param {Date|string|number} date2 - Seconda data
 * @returns {number} Differenza in minuti
 */
const diffMinutes = (date1, date2) => {
  return Math.floor(diffSeconds(date1, date2) / 60);
};

/**
 * Calcola la differenza in ore tra due date
 * @param {Date|string|number} date1 - Prima data
 * @param {Date|string|number} date2 - Seconda data
 * @returns {number} Differenza in ore
 */
const diffHours = (date1, date2) => {
  return Math.floor(diffMinutes(date1, date2) / 60);
};

/**
 * Calcola la differenza in giorni tra due date
 * @param {Date|string|number} date1 - Prima data
 * @param {Date|string|number} date2 - Seconda data
 * @returns {number} Differenza in giorni
 */
const diffDays = (date1, date2) => {
  return Math.floor(diffHours(date1, date2) / 24);
};

/**
 * Verifica se una data è nel passato
 * @param {Date|string|number} date - Data da verificare
 * @returns {boolean} true se la data è nel passato
 */
const isPast = (date) => {
  const d = date instanceof Date ? date : new Date(date);
  return d < new Date();
};

/**
 * Verifica se una data è nel futuro
 * @param {Date|string|number} date - Data da verificare
 * @returns {boolean} true se la data è nel futuro
 */
const isFuture = (date) => {
  const d = date instanceof Date ? date : new Date(date);
  return d > new Date();
};

/**
 * Verifica se una data è oggi
 * @param {Date|string|number} date - Data da verificare
 * @returns {boolean} true se la data è oggi
 */
const isToday = (date) => {
  const d = date instanceof Date ? date : new Date(date);
  const today = new Date();
  return d.getDate() === today.getDate() &&
    d.getMonth() === today.getMonth() &&
    d.getFullYear() === today.getFullYear();
};

/**
 * Verifica se una data è valida
 * @param {Date|string|number} date - Data da verificare
 * @returns {boolean} true se la data è valida
 */
const isValidDate = (date) => {
  if (date instanceof Date) {
    return !isNaN(date.getTime());
  }
  
  const d = new Date(date);
  return !isNaN(d.getTime());
};

module.exports = {
  formatISODate,
  formatLocalDate,
  formatLocalDateTime,
  formatRelativeDate,
  addMilliseconds,
  addSeconds,
  addMinutes,
  addHours,
  addDays,
  addMonths,
  addYears,
  diffMilliseconds,
  diffSeconds,
  diffMinutes,
  diffHours,
  diffDays,
  isPast,
  isFuture,
  isToday,
  isValidDate
};
