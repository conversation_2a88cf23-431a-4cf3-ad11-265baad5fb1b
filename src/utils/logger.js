const fs = require('fs');
const path = require('path');

// Crea directory logs se non esiste
const logsDir = path.join(__dirname, '../../logs');
if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
}

// Logger semplice
class Logger {
    constructor() {
        this.logFile = path.join(logsDir, 'app.log');
    }

    formatMessage(level, message) {
        const timestamp = new Date().toISOString();
        return `${timestamp}: ${timestamp.split('T')[0]} ${timestamp.split('T')[1].split('.')[0]} ${level}: ${message}`;
    }

    writeToFile(message) {
        try {
            fs.appendFileSync(this.logFile, message + '\n');
        } catch (error) {
            console.error('Errore scrittura log:', error);
        }
    }

    info(message) {
        const formattedMessage = this.formatMessage('info', message);
        console.log(formattedMessage);
        this.writeToFile(formattedMessage);
    }

    error(message, error = null) {
        const errorMessage = error ? `${message} - ${error.message || error}` : message;
        const formattedMessage = this.formatMessage('error', errorMessage);
        console.error(formattedMessage);
        this.writeToFile(formattedMessage);
    }

    warn(message) {
        const formattedMessage = this.formatMessage('warn', message);
        console.warn(formattedMessage);
        this.writeToFile(formattedMessage);
    }

    debug(message) {
        if (process.env.NODE_ENV !== 'production') {
            const formattedMessage = this.formatMessage('debug', message);
            console.log(formattedMessage);
            this.writeToFile(formattedMessage);
        }
    }
}

module.exports = new Logger();
