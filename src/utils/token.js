/**
 * Utilità per la gestione dei token
 */
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const config = require('../config/config');
const logger = require('../config/logger');

// Logger specifico per il modulo token
const tokenLogger = logger.getModuleLogger('token');

/**
 * Genera un token JWT
 * @param {Object} payload - Payload del token
 * @param {string} secret - Secret per la firma del token
 * @param {Object} options - Opzioni per il token
 * @returns {string} Token JWT
 */
const generateJwtToken = (payload, secret = config.auth.jwtSecret, options = {}) => {
  const defaultOptions = {
    expiresIn: config.auth.jwtExpiresIn
  };
  
  const tokenOptions = { ...defaultOptions, ...options };
  
  return jwt.sign(payload, secret, tokenOptions);
};

/**
 * Verifica un token JWT
 * @param {string} token - Token JWT
 * @param {string} secret - Secret per la verifica del token
 * @returns {Object} Payload del token
 */
const verifyJwtToken = (token, secret = config.auth.jwtSecret) => {
  try {
    return jwt.verify(token, secret);
  } catch (error) {
    tokenLogger.error('Errore verifica token JWT:', error);
    throw error;
  }
};

/**
 * Decodifica un token JWT senza verificarlo
 * @param {string} token - Token JWT
 * @returns {Object} Payload del token
 */
const decodeJwtToken = (token) => {
  return jwt.decode(token);
};

/**
 * Genera un token casuale
 * @param {number} size - Dimensione del token in byte
 * @returns {string} Token casuale in formato esadecimale
 */
const generateRandomToken = (size = 32) => {
  return crypto.randomBytes(size).toString('hex');
};

/**
 * Genera un token di verifica
 * @param {string} userId - ID dell'utente
 * @param {string} email - Email dell'utente
 * @param {string} type - Tipo di token
 * @param {number} expiresIn - Scadenza del token in secondi
 * @returns {string} Token di verifica
 */
const generateVerificationToken = (userId, email, type = 'email_verification', expiresIn = 86400) => {
  const payload = {
    userId,
    email,
    type
  };
  
  return generateJwtToken(payload, config.auth.jwtSecret, { expiresIn });
};

/**
 * Genera un token di reset password
 * @param {string} userId - ID dell'utente
 * @param {string} email - Email dell'utente
 * @param {number} expiresIn - Scadenza del token in secondi
 * @returns {string} Token di reset password
 */
const generatePasswordResetToken = (userId, email, expiresIn = 3600) => {
  return generateVerificationToken(userId, email, 'password_reset', expiresIn);
};

/**
 * Genera un token di refresh
 * @param {string} userId - ID dell'utente
 * @param {number} expiresIn - Scadenza del token in secondi
 * @returns {string} Token di refresh
 */
const generateRefreshToken = (userId, expiresIn = 2592000) => {
  const payload = {
    userId,
    type: 'refresh'
  };
  
  return generateJwtToken(payload, config.auth.jwtSecret, { expiresIn });
};

/**
 * Genera un token di accesso
 * @param {string} userId - ID dell'utente
 * @param {string} email - Email dell'utente
 * @param {Object} additionalData - Dati aggiuntivi
 * @param {number} expiresIn - Scadenza del token in secondi
 * @returns {string} Token di accesso
 */
const generateAccessToken = (userId, email, additionalData = {}, expiresIn = 3600) => {
  const payload = {
    userId,
    email,
    type: 'access',
    ...additionalData
  };
  
  return generateJwtToken(payload, config.auth.jwtSecret, { expiresIn });
};

/**
 * Genera un hash HMAC
 * @param {string} data - Dati da hashare
 * @param {string} secret - Secret per l'hash
 * @returns {string} Hash HMAC
 */
const generateHmac = (data, secret = config.auth.jwtSecret) => {
  return crypto.createHmac('sha256', secret).update(data).digest('hex');
};

/**
 * Genera un hash SHA-256
 * @param {string} data - Dati da hashare
 * @returns {string} Hash SHA-256
 */
const generateSha256 = (data) => {
  return crypto.createHash('sha256').update(data).digest('hex');
};

/**
 * Genera un ID univoco
 * @returns {string} ID univoco
 */
const generateUniqueId = () => {
  return crypto.randomUUID();
};

module.exports = {
  generateJwtToken,
  verifyJwtToken,
  decodeJwtToken,
  generateRandomToken,
  generateVerificationToken,
  generatePasswordResetToken,
  generateRefreshToken,
  generateAccessToken,
  generateHmac,
  generateSha256,
  generateUniqueId
};
