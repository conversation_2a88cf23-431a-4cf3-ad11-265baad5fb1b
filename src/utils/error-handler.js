/**
 * Utilità per la gestione degli errori
 */
import logger from '../config/logging/logger.js';
import config from '../config/config.js';

// Logger specifico per il modulo errori
const errorLogger = logger.getModuleLogger('error');

/**
 * Classe per gli errori API
 * @extends Error
 */
class ApiError extends Error {
  /**
   * Crea un nuovo errore API
   * @param {number} statusCode - Codice di stato HTTP
   * @param {string} message - Messaggio di errore
   * @param {Object} data - Dati aggiuntivi
   * @param {boolean} isOperational - Se l'errore è operazionale
   */
  constructor(statusCode, message, data = {}, isOperational = true) {
    super(message);
    this.statusCode = statusCode;
    this.data = data;
    this.isOperational = isOperational;
    this.name = this.constructor.name;
    Error.captureStackTrace(this, this.constructor);
  }

  /**
   * Crea un errore 400 Bad Request
   * @param {string} message - Messaggio di errore
   * @param {Object} data - <PERSON>ti aggiuntivi
   * @returns {ApiError} Errore API
   */
  static badRequest(message = 'Bad Request', data = {}) {
    return new ApiError(400, message, data);
  }

  /**
   * Crea un errore 401 Unauthorized
   * @param {string} message - Messaggio di errore
   * @param {Object} data - Dati aggiuntivi
   * @returns {ApiError} Errore API
   */
  static unauthorized(message = 'Unauthorized', data = {}) {
    return new ApiError(401, message, data);
  }

  /**
   * Crea un errore 403 Forbidden
   * @param {string} message - Messaggio di errore
   * @param {Object} data - Dati aggiuntivi
   * @returns {ApiError} Errore API
   */
  static forbidden(message = 'Forbidden', data = {}) {
    return new ApiError(403, message, data);
  }

  /**
   * Crea un errore 404 Not Found
   * @param {string} message - Messaggio di errore
   * @param {Object} data - Dati aggiuntivi
   * @returns {ApiError} Errore API
   */
  static notFound(message = 'Not Found', data = {}) {
    return new ApiError(404, message, data);
  }

  /**
   * Crea un errore 409 Conflict
   * @param {string} message - Messaggio di errore
   * @param {Object} data - Dati aggiuntivi
   * @returns {ApiError} Errore API
   */
  static conflict(message = 'Conflict', data = {}) {
    return new ApiError(409, message, data);
  }

  /**
   * Crea un errore 422 Unprocessable Entity
   * @param {string} message - Messaggio di errore
   * @param {Object} data - Dati aggiuntivi
   * @returns {ApiError} Errore API
   */
  static validationError(message = 'Validation Error', data = {}) {
    return new ApiError(422, message, data);
  }

  /**
   * Crea un errore 429 Too Many Requests
   * @param {string} message - Messaggio di errore
   * @param {Object} data - Dati aggiuntivi
   * @returns {ApiError} Errore API
   */
  static tooManyRequests(message = 'Too Many Requests', data = {}) {
    return new ApiError(429, message, data);
  }

  /**
   * Crea un errore 500 Internal Server Error
   * @param {string} message - Messaggio di errore
   * @param {Object} data - Dati aggiuntivi
   * @returns {ApiError} Errore API
   */
  static internal(message = 'Internal Server Error', data = {}) {
    return new ApiError(500, message, data, false);
  }
}

/**
 * Middleware per la gestione degli errori
 * @param {Error} err - Errore
 * @param {Object} req - Richiesta Express
 * @param {Object} res - Risposta Express
 * @param {Function} next - Funzione next di Express
 */
const errorHandler = (err, req, res, next) => {
  let error = err;

  // Se non è un errore API, lo convertiamo
  if (!(error instanceof ApiError)) {
    const statusCode = error.statusCode || 500;
    const message = error.message || 'Internal Server Error';
    error = new ApiError(statusCode, message, {}, false);
  }

  // Log dell'errore
  if (error.isOperational) {
    errorLogger.warn(`[${error.statusCode}] ${error.message}`, {
      url: req.originalUrl,
      method: req.method,
      ip: req.ip,
      data: error.data
    });
  } else {
    errorLogger.error(`[${error.statusCode}] ${error.message}`, {
      url: req.originalUrl,
      method: req.method,
      ip: req.ip,
      stack: error.stack
    });
  }

  // Risposta
  res.status(error.statusCode).json({
    status: 'error',
    statusCode: error.statusCode,
    message: error.message,
    ...(config.isDev() && { stack: error.stack }),
    ...(Object.keys(error.data).length > 0 && { data: error.data })
  });
};

/**
 * Middleware per la gestione delle route non trovate
 * @param {Object} req - Richiesta Express
 * @param {Object} res - Risposta Express
 * @param {Function} next - Funzione next di Express
 */
const notFoundHandler = (req, res, next) => {
  next(ApiError.notFound(`Route not found: ${req.originalUrl}`));
};

/**
 * Middleware per la gestione degli errori di validazione
 * @param {Object} err - Errore
 * @param {Object} req - Richiesta Express
 * @param {Object} res - Risposta Express
 * @param {Function} next - Funzione next di Express
 */
const validationErrorHandler = (err, req, res, next) => {
  if (err.name === 'ValidationError') {
    // Errore di validazione Mongoose
    const errors = Object.values(err.errors).map(error => ({
      field: error.path,
      message: error.message
    }));

    return next(ApiError.validationError('Validation Error', { errors }));
  }

  if (err.name === 'MongoError' && err.code === 11000) {
    // Errore di duplicazione MongoDB
    const field = Object.keys(err.keyValue)[0];
    const value = err.keyValue[field];

    return next(ApiError.conflict(`Duplicate value: ${value} for field: ${field}`));
  }

  next(err);
};

/**
 * Middleware per la gestione degli errori di sintassi JSON
 * @param {Object} err - Errore
 * @param {Object} req - Richiesta Express
 * @param {Object} res - Risposta Express
 * @param {Function} next - Funzione next di Express
 */
const jsonSyntaxErrorHandler = (err, req, res, next) => {
  if (err instanceof SyntaxError && err.status === 400 && 'body' in err) {
    return next(ApiError.badRequest('Invalid JSON'));
  }

  next(err);
};

/**
 * Middleware per la gestione degli errori di timeout
 * @param {Object} req - Richiesta Express
 * @param {Object} res - Risposta Express
 * @param {Function} next - Funzione next di Express
 */
const timeoutHandler = (req, res, next) => {
  req.setTimeout(30000, () => {
    next(ApiError.internal('Request Timeout'));
  });

  next();
};

export {
  ApiError,
  errorHandler,
  notFoundHandler,
  validationErrorHandler,
  jsonSyntaxErrorHandler,
  timeoutHandler
};
