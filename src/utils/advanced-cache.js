/**
 * Sistema di caching avanzato
 *
 * Questo modulo fornisce funzionalità di caching avanzate per migliorare le prestazioni
 * dell'applicazione, con supporto per diversi livelli di caching:
 * - Cache in memoria
 * - Cache Redis
 * - Cache del browser (tramite header HTTP)
 */

import crypto from 'crypto';
import config from '../config/config.js';
import redisClient from '../config/redis.js';
import logger from '../config/logger.js';

class AdvancedCache {
  constructor() {
    // Cache in memoria (Map è più performante di un oggetto per lookup frequenti)
    this.memoryCache = new Map();

    // Statistiche di utilizzo della cache
    this.stats = {
      hits: 0,
      misses: 0,
      memoryHits: 0,
      redisHits: 0,
      sets: 0,
      invalidations: 0
    };

    // Intervallo per la pulizia della cache in memoria
    this.cleanupInterval = setInterval(() => this.cleanup(), 300000); // 5 minuti

    logger.info('Sistema di caching avanzato inizializzato');
  }

  /**
   * Genera una chiave di cache univoca
   * @param {string} namespace - Namespace per raggruppare le chiavi correlate
   * @param {string|object} key - Chiave o oggetto da utilizzare per generare la chiave
   * @returns {string} Chiave di cache
   */
  generateKey(namespace, key) {
    let keyString;

    if (typeof key === 'string') {
      keyString = key;
    } else if (typeof key === 'object') {
      try {
        // Ordina le chiavi per garantire la coerenza
        const orderedObj = {};
        Object.keys(key).sort().forEach(k => {
          orderedObj[k] = key[k];
        });
        keyString = JSON.stringify(orderedObj);
      } catch (error) {
        logger.error('Errore nella generazione della chiave di cache:', error);
        keyString = crypto.randomBytes(8).toString('hex');
      }
    } else {
      keyString = String(key);
    }

    // Genera un hash per chiavi lunghe
    if (keyString.length > 100) {
      keyString = crypto.createHash('sha256').update(keyString).digest('hex');
    }

    return `${namespace}:${keyString}`;
  }

  /**
   * Ottiene un valore dalla cache
   * @param {string} namespace - Namespace della chiave
   * @param {string|object} key - Chiave o oggetto per identificare il valore
   * @param {Function} [fetchFunction] - Funzione da chiamare in caso di cache miss
   * @param {object} [options] - Opzioni di caching
   * @returns {Promise<any>} Valore dalla cache o dalla funzione di fetch
   */
  async get(namespace, key, fetchFunction = null, options = {}) {
    const cacheKey = this.generateKey(namespace, key);
    const defaultOptions = {
      ttl: 3600, // 1 ora in secondi
      useMemory: true,
      useRedis: config.redis.enabled,
      forceRefresh: false
    };

    const opts = { ...defaultOptions, ...options };

    // Se è richiesto un refresh forzato, salta la cache
    if (opts.forceRefresh) {
      return this.refreshCache(namespace, key, fetchFunction, opts);
    }

    // Controlla prima la cache in memoria (più veloce)
    if (opts.useMemory) {
      const memoryData = this.memoryCache.get(cacheKey);
      if (memoryData && memoryData.expires > Date.now()) {
        this.stats.hits++;
        this.stats.memoryHits++;
        return memoryData.value;
      }
    }

    // Controlla la cache Redis
    if (opts.useRedis && config.redis.enabled) {
      try {
        const redisData = await redisClient.getClient().get(cacheKey);
        if (redisData) {
          const parsedData = JSON.parse(redisData);

          // Aggiorna anche la cache in memoria
          if (opts.useMemory) {
            this.memoryCache.set(cacheKey, {
              value: parsedData,
              expires: Date.now() + (opts.ttl * 1000)
            });
          }

          this.stats.hits++;
          this.stats.redisHits++;
          return parsedData;
        }
      } catch (error) {
        logger.error(`Errore nel recupero dalla cache Redis: ${error.message}`);
      }
    }

    // Cache miss, chiama la funzione di fetch se fornita
    this.stats.misses++;

    if (fetchFunction) {
      return this.refreshCache(namespace, key, fetchFunction, opts);
    }

    return null;
  }

  /**
   * Aggiorna la cache con un nuovo valore
   * @param {string} namespace - Namespace della chiave
   * @param {string|object} key - Chiave o oggetto per identificare il valore
   * @param {Function} fetchFunction - Funzione da chiamare per ottenere il nuovo valore
   * @param {object} options - Opzioni di caching
   * @returns {Promise<any>} Nuovo valore
   */
  async refreshCache(namespace, key, fetchFunction, options) {
    try {
      const value = await fetchFunction();
      await this.set(namespace, key, value, options);
      return value;
    } catch (error) {
      logger.error(`Errore nel refresh della cache: ${error.message}`);
      throw error;
    }
  }

  /**
   * Imposta un valore nella cache
   * @param {string} namespace - Namespace della chiave
   * @param {string|object} key - Chiave o oggetto per identificare il valore
   * @param {any} value - Valore da memorizzare
   * @param {object} [options] - Opzioni di caching
   * @returns {Promise<void>}
   */
  async set(namespace, key, value, options = {}) {
    const cacheKey = this.generateKey(namespace, key);
    const defaultOptions = {
      ttl: 3600, // 1 ora in secondi
      useMemory: true,
      useRedis: config.redis.enabled
    };

    const opts = { ...defaultOptions, ...options };

    this.stats.sets++;

    // Memorizza nella cache in memoria
    if (opts.useMemory) {
      this.memoryCache.set(cacheKey, {
        value,
        expires: Date.now() + (opts.ttl * 1000)
      });
    }

    // Memorizza nella cache Redis
    if (opts.useRedis && config.redis.enabled) {
      try {
        await redisClient.getClient().set(
          cacheKey,
          JSON.stringify(value),
          'EX',
          opts.ttl
        );
      } catch (error) {
        logger.error(`Errore nella memorizzazione nella cache Redis: ${error.message}`);
      }
    }
  }

  /**
   * Invalida una chiave di cache
   * @param {string} namespace - Namespace della chiave
   * @param {string|object} key - Chiave o oggetto per identificare il valore
   * @returns {Promise<void>}
   */
  async invalidate(namespace, key) {
    const cacheKey = this.generateKey(namespace, key);

    this.stats.invalidations++;

    // Rimuovi dalla cache in memoria
    this.memoryCache.delete(cacheKey);

    // Rimuovi dalla cache Redis
    if (config.redis.enabled) {
      try {
        await redisClient.getClient().del(cacheKey);
      } catch (error) {
        logger.error(`Errore nell'invalidazione della cache Redis: ${error.message}`);
      }
    }
  }

  /**
   * Invalida tutte le chiavi in un namespace
   * @param {string} namespace - Namespace da invalidare
   * @returns {Promise<void>}
   */
  async invalidateNamespace(namespace) {
    // Rimuovi dalla cache in memoria
    for (const [key] of this.memoryCache) {
      if (key.startsWith(`${namespace}:`)) {
        this.memoryCache.delete(key);
        this.stats.invalidations++;
      }
    }

    // Rimuovi dalla cache Redis
    if (config.redis.enabled) {
      try {
        const keys = await redisClient.getClient().keys(`${namespace}:*`);
        if (keys.length > 0) {
          await redisClient.getClient().del(keys);
          this.stats.invalidations += keys.length;
        }
      } catch (error) {
        logger.error(`Errore nell'invalidazione del namespace Redis: ${error.message}`);
      }
    }
  }

  /**
   * Pulisce le chiavi scadute dalla cache in memoria
   */
  cleanup() {
    const now = Date.now();
    let expiredCount = 0;

    for (const [key, data] of this.memoryCache) {
      if (data.expires < now) {
        this.memoryCache.delete(key);
        expiredCount++;
      }
    }

    if (expiredCount > 0) {
      logger.debug(`Pulizia cache: rimosse ${expiredCount} chiavi scadute`);
    }
  }

  /**
   * Ottiene le statistiche di utilizzo della cache
   * @returns {object} Statistiche
   */
  getStats() {
    const hitRate = this.stats.hits + this.stats.misses > 0
      ? (this.stats.hits / (this.stats.hits + this.stats.misses) * 100).toFixed(2)
      : 0;

    return {
      ...this.stats,
      hitRate: `${hitRate}%`,
      memorySize: this.memoryCache.size,
      memoryUsage: process.memoryUsage().heapUsed
    };
  }

  /**
   * Resetta le statistiche di utilizzo della cache
   */
  resetStats() {
    this.stats = {
      hits: 0,
      misses: 0,
      memoryHits: 0,
      redisHits: 0,
      sets: 0,
      invalidations: 0
    };
  }

  /**
   * Pulisce la cache alla chiusura dell'applicazione
   */
  shutdown() {
    clearInterval(this.cleanupInterval);
    this.memoryCache.clear();
    logger.info('Sistema di caching avanzato terminato');
  }
}

// Esporta un'istanza singleton
const advancedCache = new AdvancedCache();
export default advancedCache;
