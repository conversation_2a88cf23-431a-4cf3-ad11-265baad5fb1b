/**
 * Sistema di recovery automatico
 *
 * Questo modulo fornisce funzionalità per il ripristino automatico in caso di errori
 * o situazioni anomale, migliorando la resilienza dell'applicazione.
 */

import fs from 'fs';
import path from 'path';
import { promisify } from 'util';
import { exec } from 'child_process';
import config from '../config/config.js';
import logger from '../config/logging/logger.js';
import redisClient from '../config/redis.js';
import database from '../config/database.js';

const execAsync = promisify(exec);
const writeFileAsync = promisify(fs.writeFile);
const readFileAsync = promisify(fs.readFile);

class AutoRecovery {
  constructor() {
    this.recoveryStrategies = new Map();
    this.healthChecks = new Map();
    this.recoveryHistory = [];
    this.maxHistoryLength = 100;
    this.isRecovering = false;

    // Inizializza le strategie di recovery predefinite
    this.initDefaultStrategies();

    // Inizializza i controlli di salute predefiniti
    this.initDefaultHealthChecks();

    logger.info('Sistema di recovery automatico inizializzato');
  }

  /**
   * Inizializza le strategie di recovery predefinite
   */
  initDefaultStrategies() {
    // Strategia per problemi di connessione al database
    this.registerRecoveryStrategy('database-connection', async () => {
      logger.warn('Tentativo di ripristino della connessione al database');

      try {
        // Prova a riconnettersi al database
        await database.disconnect();
        await new Promise(resolve => setTimeout(resolve, 1000));
        await database.connect();

        logger.info('Connessione al database ripristinata con successo');
        return true;
      } catch (error) {
        logger.error('Fallito ripristino della connessione al database:', error);
        return false;
      }
    });

    // Strategia per problemi di connessione a Redis
    this.registerRecoveryStrategy('redis-connection', async () => {
      logger.warn('Tentativo di ripristino della connessione a Redis');

      try {
        // Prova a riconnettersi a Redis
        await redisClient.disconnect();
        await new Promise(resolve => setTimeout(resolve, 1000));
        await redisClient.connect();

        logger.info('Connessione a Redis ripristinata con successo');
        return true;
      } catch (error) {
        logger.error('Fallito ripristino della connessione a Redis:', error);
        return false;
      }
    });

    // Strategia per problemi di memoria
    this.registerRecoveryStrategy('memory-leak', async () => {
      logger.warn('Tentativo di ripristino per problemi di memoria');

      try {
        // Forza la garbage collection (se disponibile)
        if (global.gc) {
          global.gc();
          logger.info('Garbage collection forzata eseguita');
        }

        // Pulisci le cache
        if (global.cacheManager) {
          await global.cacheManager.clear();
          logger.info('Cache pulite');
        }

        return true;
      } catch (error) {
        logger.error('Fallito ripristino per problemi di memoria:', error);
        return false;
      }
    });

    // Strategia per problemi di file system
    this.registerRecoveryStrategy('filesystem', async () => {
      logger.warn('Tentativo di ripristino per problemi di file system');

      try {
        // Verifica e ripristina i permessi delle directory critiche
        const criticalDirs = [
          path.join(process.cwd(), 'public'),
          path.join(process.cwd(), 'logs'),
          path.join(process.cwd(), 'uploads')
        ];

        for (const dir of criticalDirs) {
          if (fs.existsSync(dir)) {
            await execAsync(`chmod -R 755 "${dir}"`);
          }
        }

        logger.info('Permessi del file system ripristinati');
        return true;
      } catch (error) {
        logger.error('Fallito ripristino per problemi di file system:', error);
        return false;
      }
    });

    // Strategia per problemi di certificati SSL
    this.registerRecoveryStrategy('ssl-certificates', async () => {
      logger.warn('Tentativo di ripristino per problemi di certificati SSL');

      try {
        // Verifica i certificati SSL
        if (config.ssl.enabled) {
          const keyPath = config.ssl.keyPath;
          const certPath = config.ssl.certPath;

          if (!fs.existsSync(keyPath) || !fs.existsSync(certPath)) {
            logger.error('Certificati SSL non trovati');

            // Disabilita SSL in caso di certificati mancanti
            config.ssl.enabled = false;

            // Crea un file di stato per indicare il problema
            try {
              const sslErrorInfo = {
                timestamp: new Date().toISOString(),
                error: 'Certificati SSL non trovati',
                keyPath: keyPath,
                certPath: certPath,
                action: 'SSL disabilitato automaticamente'
              };

              // Crea la directory logs se non esiste
              if (!fs.existsSync('./logs')) {
                fs.mkdirSync('./logs', { recursive: true });
              }

              fs.writeFileSync('./logs/ssl_error.json', JSON.stringify(sslErrorInfo, null, 2));
              logger.info('Informazioni errore SSL salvate in logs/ssl_error.json');

              // Segnala che il ripristino è stato eseguito (disabilitando SSL)
              return true;
            } catch (writeError) {
              logger.error('Impossibile salvare informazioni errore SSL:', writeError);
              return false;
            }
          }

          try {
            // Verifica la validità dei certificati
            await execAsync(`openssl x509 -in "${certPath}" -noout -checkend 0`);
            logger.info('Certificati SSL validi');
            return true;
          } catch (opensslError) {
            logger.error('Certificati SSL non validi:', opensslError.message);

            // Disabilita SSL in caso di certificati non validi
            config.ssl.enabled = false;

            // Crea un file di stato per indicare il problema
            try {
              const sslErrorInfo = {
                timestamp: new Date().toISOString(),
                error: 'Certificati SSL non validi: ' + opensslError.message,
                keyPath: keyPath,
                certPath: certPath,
                action: 'SSL disabilitato automaticamente'
              };

              fs.writeFileSync('./logs/ssl_error.json', JSON.stringify(sslErrorInfo, null, 2));
              logger.info('Informazioni errore SSL salvate in logs/ssl_error.json');

              // Segnala che il ripristino è stato eseguito (disabilitando SSL)
              return true;
            } catch (writeError) {
              logger.error('Impossibile salvare informazioni errore SSL:', writeError);
              return false;
            }
          }
        }

        return true;
      } catch (error) {
        logger.error('Fallito ripristino per problemi di certificati SSL:', error);

        // Disabilita SSL in caso di errore generico
        config.ssl.enabled = false;
        logger.info('SSL disabilitato a causa di errori');

        return false;
      }
    });

    // Aggiungi strategia per WebRTC
    this.registerRecoveryStrategy('webrtc-connection', async () => {
      logger.warn('Tentativo di ripristino connessione WebRTC');
      try {
        // Forza riconnessione WebRTC
        await this.resetPeerConnections();
        return true;
      } catch (error) {
        logger.error('Fallito ripristino WebRTC:', error);
        return false;
      }
    });
  }

  /**
   * Inizializza i controlli di salute predefiniti
   */
  initDefaultHealthChecks() {
    // Controllo della connessione al database
    this.registerHealthCheck('database', async () => {
      try {
        await database.ping();
        return { status: 'ok' };
      } catch (error) {
        return {
          status: 'error',
          message: error.message,
          recoveryStrategy: 'database-connection'
        };
      }
    });

    // Controllo della connessione a Redis
    this.registerHealthCheck('redis', async () => {
      try {
        if (!config.redis.enabled) {
          return {
            status: 'skipped',
            message: 'Redis non abilitato nella configurazione'
          };
        }

        // Tenta di eseguire un ping con timeout
        const pingPromise = redisClient.ping();
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Redis ping timeout')), 3000)
        );

        try {
          await Promise.race([pingPromise, timeoutPromise]);
          return {
            status: 'ok',
            message: 'Redis connesso e funzionante'
          };
        } catch (pingError) {
          // Se il ping fallisce, verifica se stiamo usando il mock
          if (redisClient.isMockRedis && redisClient.isMockRedis()) {
            return {
              status: 'warning',
              message: 'Utilizzo Redis in modalità fallback (memoria locale)',
              details: { mockMode: true }
            };
          }

          throw pingError;
        }
      } catch (error) {
        // Verifica se l'errore è di connessione
        const isConnectionError = error.message.includes('ECONNREFUSED') ||
                                 error.message.includes('timeout') ||
                                 error.message.includes('connection');

        if (isConnectionError) {
          return {
            status: 'warning', // Downgrade da error a warning poiché abbiamo il fallback
            message: `Errore connessione Redis: ${error.message}`,
            recoveryStrategy: 'redis-connection',
            details: {
              fallbackAvailable: true,
              errorType: 'connection'
            }
          };
        }

        return {
          status: 'warning',
          message: `Errore Redis: ${error.message}`,
          recoveryStrategy: 'redis-connection'
        };
      }
    });

    // Controllo dell'utilizzo della memoria
    this.registerHealthCheck('memory', async () => {
      try {
        const memoryUsage = process.memoryUsage();
        const heapUsedPercentage = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;

        if (heapUsedPercentage > 90) {
          return {
            status: 'warning',
            message: `Utilizzo heap elevato: ${heapUsedPercentage.toFixed(2)}%`,
            recoveryStrategy: 'memory-leak'
          };
        }

        return {
          status: 'ok',
          details: {
            heapUsed: memoryUsage.heapUsed,
            heapTotal: memoryUsage.heapTotal,
            heapUsedPercentage: heapUsedPercentage.toFixed(2)
          }
        };
      } catch (error) {
        return {
          status: 'error',
          message: error.message
        };
      }
    });

    // Controllo del file system
    this.registerHealthCheck('filesystem', async () => {
      try {
        const criticalDirs = [
          path.join(process.cwd(), 'public'),
          path.join(process.cwd(), 'logs'),
          path.join(process.cwd(), 'uploads')
        ];

        for (const dir of criticalDirs) {
          if (fs.existsSync(dir)) {
            // Verifica i permessi di scrittura
            const testFile = path.join(dir, '.test-write-permission');
            await writeFileAsync(testFile, 'test');
            await fs.promises.unlink(testFile);
          }
        }

        return { status: 'ok' };
      } catch (error) {
        return {
          status: 'error',
          message: error.message,
          recoveryStrategy: 'filesystem'
        };
      }
    });

    // Controllo dei certificati SSL
    this.registerHealthCheck('ssl', async () => {
      try {
        if (!config.ssl.enabled) {
          return { status: 'skipped' };
        }

        const keyPath = config.ssl.keyPath;
        const certPath = config.ssl.certPath;

        if (!fs.existsSync(keyPath) || !fs.existsSync(certPath)) {
          return {
            status: 'error',
            message: 'Certificati SSL non trovati',
            recoveryStrategy: 'ssl-certificates'
          };
        }

        // Verifica la validità dei certificati
        await execAsync(`openssl x509 -in "${certPath}" -noout -checkend 0`);

        return { status: 'ok' };
      } catch (error) {
        return {
          status: 'error',
          message: error.message,
          recoveryStrategy: 'ssl-certificates'
        };
      }
    });
  }

  /**
   * Registra una nuova strategia di recovery
   * @param {string} name - Nome della strategia
   * @param {Function} strategy - Funzione di recovery
   */
  registerRecoveryStrategy(name, strategy) {
    this.recoveryStrategies.set(name, strategy);
    logger.debug(`Strategia di recovery '${name}' registrata`);
  }

  /**
   * Registra un nuovo controllo di salute
   * @param {string} name - Nome del controllo
   * @param {Function} check - Funzione di controllo
   */
  registerHealthCheck(name, check) {
    this.healthChecks.set(name, check);
    logger.debug(`Controllo di salute '${name}' registrato`);
  }

  /**
   * Esegue tutti i controlli di salute
   * @param {boolean} autoRecover - Se true, tenta il ripristino automatico in caso di errori
   * @returns {Promise<object>} Risultati dei controlli
   */
  async runHealthChecks(autoRecover = true) {
    const results = {};
    let needsRecovery = false;
    const recoveryStrategies = new Set();

    // Esegui tutti i controlli
    for (const [name, check] of this.healthChecks.entries()) {
      try {
        const result = await check();
        results[name] = result;

        if (result.status === 'error' || result.status === 'warning') {
          needsRecovery = true;

          if (result.recoveryStrategy) {
            recoveryStrategies.add(result.recoveryStrategy);
          }
        }
      } catch (error) {
        results[name] = {
          status: 'error',
          message: error.message
        };

        needsRecovery = true;
      }
    }

    // Tenta il ripristino automatico se necessario
    if (needsRecovery && autoRecover && !this.isRecovering) {
      this.isRecovering = true;

      try {
        for (const strategyName of recoveryStrategies) {
          const strategy = this.recoveryStrategies.get(strategyName);

          if (strategy) {
            logger.info(`Esecuzione strategia di recovery '${strategyName}'`);
            const success = await strategy();

            this.recordRecoveryAttempt(strategyName, success);

            if (success) {
              logger.info(`Strategia di recovery '${strategyName}' completata con successo`);
            } else {
              logger.warn(`Strategia di recovery '${strategyName}' fallita`);
            }
          }
        }

        // Esegui nuovamente i controlli per verificare il ripristino
        const recoveryResults = await this.runHealthChecks(false);
        results.recoveryResults = recoveryResults;
      } finally {
        this.isRecovering = false;
      }
    }

    return results;
  }

  /**
   * Registra un tentativo di ripristino
   * @param {string} strategy - Nome della strategia
   * @param {boolean} success - Se il tentativo ha avuto successo
   */
  recordRecoveryAttempt(strategy, success) {
    this.recoveryHistory.push({
      timestamp: Date.now(),
      strategy,
      success
    });

    // Limita la dimensione della cronologia
    if (this.recoveryHistory.length > this.maxHistoryLength) {
      this.recoveryHistory.shift();
    }
  }

  /**
   * Ottiene la cronologia dei tentativi di ripristino
   * @returns {Array} Cronologia dei tentativi
   */
  getRecoveryHistory() {
    return this.recoveryHistory;
  }
}

// Esporta un'istanza singleton
const autoRecovery = new AutoRecovery();
export default autoRecovery;
