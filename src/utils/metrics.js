/**
 * Utilità per la gestione delle metriche
 */
import promClient from 'prom-client';
import config from '../config/config.js';
import logger from '../config/logging/logger.js';

// Logger specifico per il modulo metriche
const metricsLogger = logger.getModuleLogger('metrics');

// Registro delle metriche
const register = new promClient.Registry();

// Aggiungi le metriche di default
promClient.collectDefaultMetrics({ register });

// Contatore delle richieste HTTP
const httpRequestsTotal = new promClient.Counter({
  name: 'http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status'],
  registers: [register]
});

// Istogramma della durata delle richieste HTTP
const httpRequestDurationSeconds = new promClient.Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status'],
  buckets: [0.01, 0.05, 0.1, 0.5, 1, 2, 5, 10],
  registers: [register]
});

// Contatore degli errori
const errorsTotal = new promClient.Counter({
  name: 'errors_total',
  help: 'Total number of errors',
  labelNames: ['type', 'code'],
  registers: [register]
});

// Gauge degli utenti online
const onlineUsersGauge = new promClient.Gauge({
  name: 'online_users',
  help: 'Number of online users',
  registers: [register]
});

// Contatore delle sessioni
const sessionsTotal = new promClient.Counter({
  name: 'sessions_total',
  help: 'Total number of sessions',
  labelNames: ['status'],
  registers: [register]
});

// Istogramma della durata delle sessioni
const sessionDurationSeconds = new promClient.Histogram({
  name: 'session_duration_seconds',
  help: 'Duration of sessions in seconds',
  buckets: [60, 300, 600, 1800, 3600, 7200, 14400],
  registers: [register]
});

// Contatore delle transazioni
const transactionsTotal = new promClient.Counter({
  name: 'transactions_total',
  help: 'Total number of transactions',
  labelNames: ['type', 'status'],
  registers: [register]
});

// Istogramma dell'importo delle transazioni
const transactionAmountEur = new promClient.Histogram({
  name: 'transaction_amount_eur',
  help: 'Amount of transactions in EUR',
  buckets: [5, 10, 20, 50, 100, 200, 500],
  registers: [register]
});

// Contatore dei crediti
const creditsTotal = new promClient.Counter({
  name: 'credits_total',
  help: 'Total number of credits',
  labelNames: ['operation'],
  registers: [register]
});

// Contatore dei report
const reportsTotal = new promClient.Counter({
  name: 'reports_total',
  help: 'Total number of reports',
  labelNames: ['reason', 'status'],
  registers: [register]
});

/**
 * Middleware per la raccolta delle metriche delle richieste HTTP
 * @param {Object} req - Richiesta Express
 * @param {Object} res - Risposta Express
 * @param {Function} next - Funzione next di Express
 */
const httpMetricsMiddleware = (req, res, next) => {
  if (!config.monitoring.enableMetrics) {
    return next();
  }

  const start = Date.now();

  // Aggiungi un listener per il completamento della risposta
  res.on('finish', () => {
    const duration = (Date.now() - start) / 1000;
    const route = req.route ? req.baseUrl + req.route.path : req.path;

    // Incrementa il contatore delle richieste
    httpRequestsTotal.inc({
      method: req.method,
      route,
      status: res.statusCode
    });

    // Registra la durata della richiesta
    httpRequestDurationSeconds.observe(
      {
        method: req.method,
        route,
        status: res.statusCode
      },
      duration
    );
  });

  next();
};

/**
 * Incrementa il contatore degli errori
 * @param {string} type - Tipo di errore
 * @param {string|number} code - Codice di errore
 */
const incrementErrorsCounter = (type, code) => {
  if (!config.monitoring.enableMetrics) {
    return;
  }

  errorsTotal.inc({ type, code });
};

/**
 * Imposta il numero di utenti online
 * @param {number} count - Numero di utenti online
 */
const setOnlineUsers = (count) => {
  if (!config.monitoring.enableMetrics) {
    return;
  }

  onlineUsersGauge.set(count);
};

/**
 * Incrementa il contatore delle sessioni
 * @param {string} status - Stato della sessione
 */
const incrementSessionsCounter = (status = 'active') => {
  if (!config.monitoring.enableMetrics) {
    return;
  }

  sessionsTotal.inc({ status });
};

/**
 * Registra la durata di una sessione
 * @param {number} duration - Durata della sessione in secondi
 */
const observeSessionDuration = (duration) => {
  if (!config.monitoring.enableMetrics) {
    return;
  }

  sessionDurationSeconds.observe(duration);
};

/**
 * Incrementa il contatore delle transazioni
 * @param {string} type - Tipo di transazione
 * @param {string} status - Stato della transazione
 */
const incrementTransactionsCounter = (type, status) => {
  if (!config.monitoring.enableMetrics) {
    return;
  }

  transactionsTotal.inc({ type, status });
};

/**
 * Registra l'importo di una transazione
 * @param {number} amount - Importo della transazione in EUR
 */
const observeTransactionAmount = (amount) => {
  if (!config.monitoring.enableMetrics) {
    return;
  }

  transactionAmountEur.observe(amount);
};

/**
 * Incrementa il contatore dei crediti
 * @param {string} operation - Operazione sui crediti
 * @param {number} amount - Quantità di crediti
 */
const incrementCreditsCounter = (operation, amount) => {
  if (!config.monitoring.enableMetrics) {
    return;
  }

  creditsTotal.inc({ operation }, amount);
};

/**
 * Incrementa il contatore dei report
 * @param {string} reason - Motivo del report
 * @param {string} status - Stato del report
 */
const incrementReportsCounter = (reason, status) => {
  if (!config.monitoring.enableMetrics) {
    return;
  }

  reportsTotal.inc({ reason, status });
};

/**
 * Middleware per esporre le metriche
 * @param {Object} req - Richiesta Express
 * @param {Object} res - Risposta Express
 */
const metricsEndpoint = async (req, res) => {
  if (!config.monitoring.enableMetrics) {
    return res.status(404).send('Metrics not enabled');
  }

  try {
    res.set('Content-Type', register.contentType);
    res.end(await register.metrics());
  } catch (error) {
    metricsLogger.error('Errore esposizione metriche:', error);
    res.status(500).send('Error collecting metrics');
  }
};

export {
  register,
  httpMetricsMiddleware,
  incrementErrorsCounter,
  setOnlineUsers,
  incrementSessionsCounter,
  observeSessionDuration,
  incrementTransactionsCounter,
  observeTransactionAmount,
  incrementCreditsCounter,
  incrementReportsCounter,
  metricsEndpoint
};
