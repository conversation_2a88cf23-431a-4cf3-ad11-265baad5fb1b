/**
 * Modulo per la gestione avanzata degli header di sicurezza
 * 
 * Fornisce middleware e utilità per configurare header di sicurezza avanzati,
 * inclusa una Content Security Policy (CSP) dinamica e adattiva.
 */

'use strict';

const crypto = require('crypto');
const config = require('../config/config');
const logger = require('../config/logger');

/**
 * Genera un nonce casuale per CSP
 * @returns {string} Nonce generato
 */
function generateNonce() {
  return crypto.randomBytes(16).toString('base64');
}

/**
 * Genera un hash SHA-256 per CSP
 * @param {string} content - Contenuto da cui generare l'hash
 * @returns {string} Hash SHA-256 formattato per CSP
 */
function generateHash(content) {
  return `'sha256-${crypto.createHash('sha256').update(content).digest('base64')}'`;
}

/**
 * Costruisce una Content Security Policy dinamica
 * @param {object} req - Oggetto richiesta Express
 * @param {object} res - Oggetto risposta Express
 * @returns {string} Policy CSP
 */
function buildCSP(req, res) {
  // Genera un nonce per questa richiesta
  const nonce = generateNonce();
  res.locals.cspNonce = nonce;
  
  // Domini consentiti
  const domain = config.app.domain;
  const allowedDomains = [
    `https://${domain}`,
    `https://www.${domain}`,
    `wss://${domain}`,
    `wss://www.${domain}`
  ];
  // Rimuovi localhost in produzione
  
  // Configura le direttive CSP
  const directives = {
    'default-src': ["'self'", ...allowedDomains],
    'script-src': [
      "'self'",
      `'nonce-${nonce}'`,
      // Hash per script inline essenziali
      "'sha256-4axJXpXZxVULPARLwmYz4fzgJb/mpZcIObxUcxhWWVc='", // Per il loader
      "'sha256-/SpqH0bikxMJ4U8/VIrM8GjDRFT9ZF5wJSUQkOLeLx0='", // Per il service worker
      ...(config.isDev() ? ["'unsafe-eval'"] : []) // Consenti eval solo in sviluppo
    ],
    'connect-src': [
      "'self'",
      ...allowedDomains,
      "https://fonts.googleapis.com",
      "https://fonts.gstatic.com",
      "stun:*.google.com:*",
      "turn:turn.videochatcouple.com:*",
      "turns:turn.videochatcouple.com:*"
    ],
    'style-src': [
      "'self'",
      "https://fonts.googleapis.com",
      "'unsafe-inline'" // Necessario per gli stili inline
    ],
    'font-src': [
      "'self'",
      "https://fonts.gstatic.com",
      "data:"
    ],
    'img-src': [
      "'self'",
      "data:",
      "blob:",
      "https:"
    ],
    'media-src': [
      "'self'",
      "blob:",
      "mediastream:"
    ],
    'frame-src': [
      "'self'"
    ],
    'worker-src': [
      "'self'",
      "blob:"
    ],
    'manifest-src': ["'self'"],
    'object-src': ["'none'"],
    'base-uri': ["'self'"],
    'form-action': ["'self'"],
    'frame-ancestors': ["'none'"],
    'upgrade-insecure-requests': [],
    'block-all-mixed-content': []
  };
  
  // Costruisci la stringa CSP
  return Object.entries(directives)
    .map(([key, values]) => {
      if (values.length === 0) return key;
      return `${key} ${values.join(' ')}`;
    })
    .join('; ');
}

/**
 * Middleware per impostare header di sicurezza avanzati
 * @param {object} options - Opzioni di configurazione
 * @returns {Function} Middleware Express
 */
function securityHeaders(options = {}) {
  const defaultOptions = {
    enableCSP: true,
    enableHSTS: config.isProd(),
    enableXFrame: true,
    enableXSS: true,
    enableNoSniff: true,
    enableReferrerPolicy: true,
    reportOnly: false,
    reportUri: '/api/security/report'
  };
  
  const opts = { ...defaultOptions, ...options };
  
  return (req, res, next) => {
    // Content Security Policy
    if (opts.enableCSP) {
      const csp = buildCSP(req, res);
      const headerName = opts.reportOnly
        ? 'Content-Security-Policy-Report-Only'
        : 'Content-Security-Policy';
      
      res.setHeader(headerName, csp);
    }
    
    // HTTP Strict Transport Security
    if (opts.enableHSTS && config.isProd()) {
      res.setHeader(
        'Strict-Transport-Security',
        'max-age=31536000; includeSubDomains; preload'
      );
    }
    
    // X-Frame-Options
    if (opts.enableXFrame) {
      res.setHeader('X-Frame-Options', 'DENY');
    }
    
    // X-XSS-Protection
    if (opts.enableXSS) {
      res.setHeader('X-XSS-Protection', '1; mode=block');
    }
    
    // X-Content-Type-Options
    if (opts.enableNoSniff) {
      res.setHeader('X-Content-Type-Options', 'nosniff');
    }
    
    // Referrer-Policy
    if (opts.enableReferrerPolicy) {
      res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    }
    
    // Permissions-Policy (ex Feature-Policy)
    res.setHeader(
      'Permissions-Policy',
      'camera=self, microphone=self, geolocation=self, fullscreen=self'
    );
    
    // Cross-Origin-Embedder-Policy
    res.setHeader('Cross-Origin-Embedder-Policy', 'require-corp');
    
    // Cross-Origin-Opener-Policy
    res.setHeader('Cross-Origin-Opener-Policy', 'same-origin');
    
    // Cross-Origin-Resource-Policy
    res.setHeader('Cross-Origin-Resource-Policy', 'same-site');
    
    next();
  };
}

/**
 * Middleware per gestire le violazioni CSP
 * @returns {Function} Middleware Express
 */
function cspViolationHandler() {
  return (req, res) => {
    try {
      const violation = req.body;
      logger.warn('Violazione CSP rilevata:', {
        'blocked-uri': violation['blocked-uri'],
        'violated-directive': violation['violated-directive'],
        'document-uri': violation['document-uri']
      });
      
      res.status(204).end();
    } catch (error) {
      logger.error('Errore nella gestione della violazione CSP:', error);
      res.status(400).end();
    }
  };
}

module.exports = {
  securityHeaders,
  cspViolationHandler,
  generateNonce,
  generateHash
};
