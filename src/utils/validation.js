/**
 * Utilità per la validazione
 */
const { validationResult } = require('express-validator');

/**
 * Middleware per gestire gli errori di validazione
 * @param {Object} req - Richiesta Express
 * @param {Object} res - Risposta Express
 * @param {Function} next - Funzione next di Express
 */
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

/**
 * Verifica se una stringa è un ObjectId MongoDB valido
 * @param {string} id - ID da verificare
 * @returns {boolean} true se l'ID è valido
 */
const isValidObjectId = (id) => {
  const ObjectId = require('mongoose').Types.ObjectId;
  if (ObjectId.isValid(id)) {
    return String(new ObjectId(id)) === id;
  }
  return false;
};

/**
 * Verifica se una stringa è un indirizzo email valido
 * @param {string} email - Email da verificare
 * @returns {boolean} true se l'email è valida
 */
const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Verifica se una stringa è una password valida
 * @param {string} password - Password da verificare
 * @returns {Object} Risultato della validazione
 */
const isValidPassword = (password) => {
  const result = {
    isValid: true,
    errors: []
  };

  if (!password || password.length < 8) {
    result.isValid = false;
    result.errors.push('La password deve essere di almeno 8 caratteri');
  }

  if (!/[A-Z]/.test(password)) {
    result.isValid = false;
    result.errors.push('La password deve contenere almeno una lettera maiuscola');
  }

  if (!/[a-z]/.test(password)) {
    result.isValid = false;
    result.errors.push('La password deve contenere almeno una lettera minuscola');
  }

  if (!/\d/.test(password)) {
    result.isValid = false;
    result.errors.push('La password deve contenere almeno un numero');
  }

  return result;
};

/**
 * Verifica se una stringa è un URL valido
 * @param {string} url - URL da verificare
 * @returns {boolean} true se l'URL è valido
 */
const isValidUrl = (url) => {
  try {
    new URL(url);
    return true;
  } catch (error) {
    return false;
  }
};

/**
 * Sanitizza una stringa per prevenire XSS
 * @param {string} str - Stringa da sanitizzare
 * @returns {string} Stringa sanitizzata
 */
const sanitizeString = (str) => {
  if (!str) return '';
  
  return String(str)
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
};

/**
 * Sanitizza un oggetto per prevenire XSS
 * @param {Object} obj - Oggetto da sanitizzare
 * @returns {Object} Oggetto sanitizzato
 */
const sanitizeObject = (obj) => {
  if (!obj || typeof obj !== 'object') return obj;
  
  const sanitized = {};
  
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const value = obj[key];
      
      if (typeof value === 'string') {
        sanitized[key] = sanitizeString(value);
      } else if (Array.isArray(value)) {
        sanitized[key] = value.map(item => {
          if (typeof item === 'string') {
            return sanitizeString(item);
          } else if (typeof item === 'object') {
            return sanitizeObject(item);
          }
          return item;
        });
      } else if (typeof value === 'object' && value !== null) {
        sanitized[key] = sanitizeObject(value);
      } else {
        sanitized[key] = value;
      }
    }
  }
  
  return sanitized;
};

/**
 * Middleware per sanitizzare il corpo della richiesta
 * @param {Object} req - Richiesta Express
 * @param {Object} res - Risposta Express
 * @param {Function} next - Funzione next di Express
 */
const sanitizeBody = (req, res, next) => {
  if (req.body) {
    req.body = sanitizeObject(req.body);
  }
  next();
};

/**
 * Middleware per sanitizzare i parametri della query
 * @param {Object} req - Richiesta Express
 * @param {Object} res - Risposta Express
 * @param {Function} next - Funzione next di Express
 */
const sanitizeQuery = (req, res, next) => {
  if (req.query) {
    req.query = sanitizeObject(req.query);
  }
  next();
};

/**
 * Middleware per sanitizzare i parametri della richiesta
 * @param {Object} req - Richiesta Express
 * @param {Object} res - Risposta Express
 * @param {Function} next - Funzione next di Express
 */
const sanitizeParams = (req, res, next) => {
  if (req.params) {
    req.params = sanitizeObject(req.params);
  }
  next();
};

/**
 * Middleware per sanitizzare tutte le parti della richiesta
 * @param {Object} req - Richiesta Express
 * @param {Object} res - Risposta Express
 * @param {Function} next - Funzione next di Express
 */
const sanitizeRequest = [
  sanitizeBody,
  sanitizeQuery,
  sanitizeParams
];

module.exports = {
  handleValidationErrors,
  isValidObjectId,
  isValidEmail,
  isValidPassword,
  isValidUrl,
  sanitizeString,
  sanitizeObject,
  sanitizeBody,
  sanitizeQuery,
  sanitizeParams,
  sanitizeRequest
};
