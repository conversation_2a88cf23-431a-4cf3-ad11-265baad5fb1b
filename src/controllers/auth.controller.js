/**
 * Controller per l'autenticazione
 */
import jwt from 'jsonwebtoken';
import { validationResult } from 'express-validator';
import User from '../models/user.model.js';
import config from '../config/config.js';
import logger from '../config/logging/logger.js';
import * as emailService from '../services/email.service.js';

// Logger specifico per il modulo auth
const authLogger = logger.getModuleLogger('auth');

/**
 * Registra un nuovo utente
 * @param {Object} req - Richiesta Express
 * @param {Object} res - Risposta Express
 * @returns {Object} Risposta JSON
 */
const register = async (req, res) => {
  try {
    // Verifica errori di validazione
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { email, password, gender } = req.body;

    // Verifica se l'utente esiste già
    const existingUser = await User.findByEmail(email);
    if (existingUser) {
      return res.status(400).json({ message: 'Email gi<PERSON> registrata' });
    }

    // Crea un nuovo utente
    const user = new User({
      email,
      password,
      gender,
      credits: config.app.initialCredits,
      freeClicks: config.app.freeClicks
    });

    // Genera token di verifica
    const verificationToken = user.generateVerificationToken();

    // Salva l'utente
    await user.save();

    // Invia email di verifica
    try {
      await emailService.sendVerificationEmail(user.email, verificationToken);
    } catch (emailError) {
      authLogger.error('Errore invio email di verifica:', emailError);
      // Continuiamo comunque con la registrazione
    }

    // Genera JWT token
    const token = jwt.sign(
      { id: user._id, email: user.email },
      config.auth.jwtSecret,
      { expiresIn: config.auth.jwtExpiresIn }
    );

    authLogger.info(`Nuovo utente registrato: ${email}`);

    // Risposta
    res.status(201).json({
      message: 'Registrazione completata. Controlla la tua email per verificare l\'account.',
      token,
      user: {
        email: user.email,
        gender: user.gender,
        credits: user.credits,
        freeClicks: user.freeClicks,
        premium: user.premium,
        verified: user.verified
      }
    });
  } catch (error) {
    authLogger.error('Errore registrazione:', error);
    res.status(500).json({ message: 'Errore del server durante la registrazione' });
  }
};

/**
 * Effettua il login di un utente
 * @param {Object} req - Richiesta Express
 * @param {Object} res - Risposta Express
 * @returns {Object} Risposta JSON
 */
const login = async (req, res) => {
  try {
    // Verifica errori di validazione
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { email, password } = req.body;

    // Trova l'utente
    const user = await User.findByEmail(email);
    if (!user || !await user.comparePassword(password)) {
      return res.status(401).json({ message: 'Credenziali non valide' });
    }

    // Verifica stato account
    if (user.status !== 'active') {
      return res.status(403).json({ message: 'Account non attivo' });
    }

    // Aggiorna ultimo login
    user.lastLogin = new Date();

    // Salva informazioni sulla sessione
    user.sessions.push({
      ip: req.ip,
      userAgent: req.headers['user-agent']
    });

    // Limita il numero di sessioni memorizzate
    if (user.sessions.length > 5) {
      user.sessions = user.sessions.slice(-5);
    }

    await user.save();

    // Genera JWT token
    const token = jwt.sign(
      { id: user._id, email: user.email },
      config.auth.jwtSecret,
      { expiresIn: config.auth.jwtExpiresIn }
    );

    authLogger.info(`Login utente: ${email}`);

    // Risposta
    res.json({
      message: 'Login effettuato',
      token,
      user: {
        email: user.email,
        gender: user.gender,
        credits: user.credits,
        freeClicks: user.freeClicks,
        premium: user.premium,
        verified: user.verified
      }
    });
  } catch (error) {
    authLogger.error('Errore login:', error);
    res.status(500).json({ message: 'Errore del server durante il login' });
  }
};

/**
 * Verifica l'account di un utente
 * @param {Object} req - Richiesta Express
 * @param {Object} res - Risposta Express
 * @returns {Object} Risposta JSON
 */
const verifyAccount = async (req, res) => {
  try {
    const { token } = req.params;

    // Trova l'utente con il token di verifica
    const user = await User.findByVerificationToken(token);
    if (!user) {
      return res.status(400).json({ message: 'Token di verifica non valido o scaduto' });
    }

    // Aggiorna lo stato dell'utente
    user.verified = true;
    user.verificationToken = undefined;
    user.verificationExpires = undefined;
    await user.save();

    authLogger.info(`Account verificato: ${user.email}`);

    // Risposta
    res.json({ message: 'Account verificato con successo' });
  } catch (error) {
    authLogger.error('Errore verifica account:', error);
    res.status(500).json({ message: 'Errore del server durante la verifica dell\'account' });
  }
};

/**
 * Richiede il reset della password
 * @param {Object} req - Richiesta Express
 * @param {Object} res - Risposta Express
 * @returns {Object} Risposta JSON
 */
const forgotPassword = async (req, res) => {
  try {
    const { email } = req.body;

    // Trova l'utente
    const user = await User.findByEmail(email);
    if (!user) {
      // Per sicurezza, non rivelare che l'email non esiste
      return res.json({ message: 'Se l\'email esiste, riceverai istruzioni per reimpostare la password' });
    }

    // Genera token di reset
    const resetToken = user.generatePasswordResetToken();
    await user.save();

    // Invia email di reset
    try {
      await emailService.sendPasswordResetEmail(user.email, resetToken);
    } catch (emailError) {
      authLogger.error('Errore invio email di reset password:', emailError);
      return res.status(500).json({ message: 'Errore nell\'invio dell\'email di reset' });
    }

    authLogger.info(`Richiesta reset password: ${email}`);

    // Risposta
    res.json({ message: 'Se l\'email esiste, riceverai istruzioni per reimpostare la password' });
  } catch (error) {
    authLogger.error('Errore richiesta reset password:', error);
    res.status(500).json({ message: 'Errore del server durante la richiesta di reset password' });
  }
};

/**
 * Reimposta la password
 * @param {Object} req - Richiesta Express
 * @param {Object} res - Risposta Express
 * @returns {Object} Risposta JSON
 */
const resetPassword = async (req, res) => {
  try {
    const { token } = req.params;
    const { password } = req.body;

    // Trova l'utente con il token di reset
    const user = await User.findByResetPasswordToken(token);
    if (!user) {
      return res.status(400).json({ message: 'Token di reset non valido o scaduto' });
    }

    // Aggiorna la password
    user.password = password;
    user.resetPasswordToken = undefined;
    user.resetPasswordExpires = undefined;
    await user.save();

    authLogger.info(`Password reimpostata: ${user.email}`);

    // Risposta
    res.json({ message: 'Password reimpostata con successo' });
  } catch (error) {
    authLogger.error('Errore reset password:', error);
    res.status(500).json({ message: 'Errore del server durante il reset della password' });
  }
};

/**
 * Ottiene il profilo dell'utente corrente
 * @param {Object} req - Richiesta Express
 * @param {Object} res - Risposta Express
 * @returns {Object} Risposta JSON
 */
const getProfile = async (req, res) => {
  try {
    // L'utente è già stato caricato dal middleware auth
    const user = req.user;

    // Risposta
    res.json({
      user: {
        email: user.email,
        gender: user.gender,
        credits: user.credits,
        freeClicks: user.freeClicks,
        premium: user.premium,
        verified: user.verified,
        lastLogin: user.lastLogin,
        profile: user.profile
      }
    });
  } catch (error) {
    authLogger.error('Errore recupero profilo:', error);
    res.status(500).json({ message: 'Errore del server durante il recupero del profilo' });
  }
};

export {
  register,
  login,
  verifyAccount,
  forgotPassword,
  resetPassword,
  getProfile
};
