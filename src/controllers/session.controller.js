/**
 * Controller per le sessioni
 */
import { validationResult } from 'express-validator';
import Session from '../models/session.model.js';
import User from '../models/user.model.js';
import * as paymentService from '../services/payment.service.js';
import * as moderationService from '../services/moderation.service.js';
import logger from '../config/logger.js';

// Logger specifico per il modulo sessioni
const sessionLogger = logger.getModuleLogger('session');

/**
 * Crea una nuova sessione
 * @param {Object} req - Richiesta Express
 * @param {Object} res - Risposta Express
 * @returns {Object} Risposta JSON
 */
const createSession = async (req, res) => {
  try {
    // Verifica errori di validazione
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { partnerId } = req.body;
    const userId = req.userId;

    // Verifica che l'utente partner esista
    const partner = await User.findById(partnerId);
    if (!partner) {
      return res.status(404).json({ message: 'Partner non trovato' });
    }

    // Verifica che l'utente abbia crediti sufficienti
    const user = await User.findById(userId);
    if (!user.hasEnoughCredits(1)) {
      return res.status(402).json({ message: 'Crediti insufficienti' });
    }

    // Crea la sessione
    const session = new Session({
      userId,
      partnerId,
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    });

    await session.save();

    // Crea una transazione di utilizzo
    await paymentService.createUsageTransaction({
      userId,
      credits: 1,
      description: 'Avvio sessione video',
      sessionId: session._id,
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    });

    sessionLogger.info(`Nuova sessione creata: ${session._id}`);

    // Risposta
    res.status(201).json({
      message: 'Sessione creata',
      session: {
        id: session._id,
        startTime: session.startTime,
        status: session.status
      }
    });
  } catch (error) {
    sessionLogger.error('Errore creazione sessione:', error);
    res.status(500).json({ message: 'Errore del server durante la creazione della sessione' });
  }
};

/**
 * Termina una sessione
 * @param {Object} req - Richiesta Express
 * @param {Object} res - Risposta Express
 * @returns {Object} Risposta JSON
 */
const endSession = async (req, res) => {
  try {
    const { sessionId } = req.params;
    const userId = req.userId;

    // Trova la sessione
    const session = await Session.findById(sessionId);
    if (!session) {
      return res.status(404).json({ message: 'Sessione non trovata' });
    }

    // Verifica che l'utente sia autorizzato
    if (session.userId.toString() !== userId && session.partnerId.toString() !== userId) {
      return res.status(403).json({ message: 'Non autorizzato a terminare questa sessione' });
    }

    // Verifica che la sessione sia attiva
    if (session.status !== 'active') {
      return res.status(400).json({ message: 'La sessione è già terminata' });
    }

    // Termina la sessione
    await session.endSession({
      status: 'ended',
      reason: 'user_ended'
    });

    sessionLogger.info(`Sessione terminata: ${session._id}`);

    // Risposta
    res.json({
      message: 'Sessione terminata',
      session: {
        id: session._id,
        startTime: session.startTime,
        endTime: session.endTime,
        duration: session.duration,
        status: session.status
      }
    });
  } catch (error) {
    sessionLogger.error('Errore terminazione sessione:', error);
    res.status(500).json({ message: 'Errore del server durante la terminazione della sessione' });
  }
};

/**
 * Valuta una sessione
 * @param {Object} req - Richiesta Express
 * @param {Object} res - Risposta Express
 * @returns {Object} Risposta JSON
 */
const rateSession = async (req, res) => {
  try {
    // Verifica errori di validazione
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { sessionId } = req.params;
    const { rating, feedback } = req.body;
    const userId = req.userId;

    // Trova la sessione
    const session = await Session.findById(sessionId);
    if (!session) {
      return res.status(404).json({ message: 'Sessione non trovata' });
    }

    // Verifica che l'utente sia autorizzato
    if (session.userId.toString() !== userId && session.partnerId.toString() !== userId) {
      return res.status(403).json({ message: 'Non autorizzato a valutare questa sessione' });
    }

    // Verifica che la sessione sia terminata
    if (session.status === 'active') {
      return res.status(400).json({ message: 'Non puoi valutare una sessione attiva' });
    }

    // Aggiorna la valutazione
    if (session.userId.toString() === userId) {
      session.userRating = rating;
      session.userFeedback = feedback;
    } else {
      session.partnerRating = rating;
      session.partnerFeedback = feedback;
    }

    // Aggiungi evento di valutazione
    await session.addEvent('rating', { rating, feedback });

    await session.save();

    sessionLogger.info(`Sessione valutata: ${session._id}`);

    // Risposta
    res.json({
      message: 'Sessione valutata',
      session: {
        id: session._id,
        userRating: session.userRating,
        partnerRating: session.partnerRating
      }
    });
  } catch (error) {
    sessionLogger.error('Errore valutazione sessione:', error);
    res.status(500).json({ message: 'Errore del server durante la valutazione della sessione' });
  }
};

/**
 * Segnala una sessione
 * @param {Object} req - Richiesta Express
 * @param {Object} res - Risposta Express
 * @returns {Object} Risposta JSON
 */
const reportSession = async (req, res) => {
  try {
    // Verifica errori di validazione
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { sessionId } = req.params;
    const { reason, description } = req.body;
    const userId = req.userId;

    // Trova la sessione
    const session = await Session.findById(sessionId);
    if (!session) {
      return res.status(404).json({ message: 'Sessione non trovata' });
    }

    // Verifica che l'utente sia autorizzato
    if (session.userId.toString() !== userId && session.partnerId.toString() !== userId) {
      return res.status(403).json({ message: 'Non autorizzato a segnalare questa sessione' });
    }

    // Determina l'utente segnalato
    const reportedId = session.userId.toString() === userId ? session.partnerId : session.userId;

    // Crea il report
    const report = await moderationService.createReport({
      reporterId: userId,
      reportedId,
      sessionId: session._id,
      reason,
      description,
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    });

    sessionLogger.info(`Sessione segnalata: ${session._id}`);

    // Risposta
    res.status(201).json({
      message: 'Sessione segnalata',
      report: {
        id: report._id,
        reason: report.reason,
        status: report.status
      }
    });
  } catch (error) {
    sessionLogger.error('Errore segnalazione sessione:', error);
    res.status(500).json({ message: 'Errore del server durante la segnalazione della sessione' });
  }
};

/**
 * Ottiene le sessioni dell'utente
 * @param {Object} req - Richiesta Express
 * @param {Object} res - Risposta Express
 * @returns {Object} Risposta JSON
 */
const getUserSessions = async (req, res) => {
  try {
    const userId = req.userId;
    const { limit = 10, skip = 0, status } = req.query;

    // Costruisci la query
    const query = { userId };

    if (status) {
      query.status = status;
    }

    // Esegui la query
    const sessions = await Session.find(query)
      .sort({ startTime: -1 })
      .limit(parseInt(limit))
      .skip(parseInt(skip))
      .populate('partnerId', 'email');

    // Conta il totale
    const total = await Session.countDocuments(query);

    // Risposta
    res.json({
      sessions,
      total,
      limit: parseInt(limit),
      skip: parseInt(skip)
    });
  } catch (error) {
    sessionLogger.error('Errore recupero sessioni utente:', error);
    res.status(500).json({ message: 'Errore del server durante il recupero delle sessioni' });
  }
};

/**
 * Ottiene una sessione specifica
 * @param {Object} req - Richiesta Express
 * @param {Object} res - Risposta Express
 * @returns {Object} Risposta JSON
 */
const getSession = async (req, res) => {
  try {
    const { sessionId } = req.params;
    const userId = req.userId;

    // Trova la sessione
    const session = await Session.findById(sessionId)
      .populate('userId', 'email')
      .populate('partnerId', 'email');

    if (!session) {
      return res.status(404).json({ message: 'Sessione non trovata' });
    }

    // Verifica che l'utente sia autorizzato
    if (session.userId._id.toString() !== userId && session.partnerId._id.toString() !== userId) {
      return res.status(403).json({ message: 'Non autorizzato ad accedere a questa sessione' });
    }

    // Risposta
    res.json({ session });
  } catch (error) {
    sessionLogger.error('Errore recupero sessione:', error);
    res.status(500).json({ message: 'Errore del server durante il recupero della sessione' });
  }
};

/**
 * Ottiene le statistiche delle sessioni dell'utente
 * @param {Object} req - Richiesta Express
 * @param {Object} res - Risposta Express
 * @returns {Object} Risposta JSON
 */
const getUserSessionStats = async (req, res) => {
  try {
    const userId = req.userId;

    // Ottieni le statistiche
    const stats = await Session.getUserStats(userId);

    // Risposta
    res.json({ stats });
  } catch (error) {
    sessionLogger.error('Errore recupero statistiche sessioni:', error);
    res.status(500).json({ message: 'Errore del server durante il recupero delle statistiche' });
  }
};

export {
  createSession,
  endSession,
  rateSession,
  reportSession,
  getUserSessions,
  getSession,
  getUserSessionStats
};
