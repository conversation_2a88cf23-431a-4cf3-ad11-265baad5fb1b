/**
 * Controller per gli utenti
 */
import { validationResult } from 'express-validator';
import User from '../models/user.model.js';
import Transaction from '../models/transaction.model.js';
import * as paymentService from '../services/payment.service.js';
import logger from '../config/logging/logger.js';

// Logger specifico per il modulo utenti
const userLogger = logger.getModuleLogger('user');

/**
 * Ottiene il profilo dell'utente
 * @param {Object} req - Richiesta Express
 * @param {Object} res - Risposta Express
 * @returns {Object} Risposta JSON
 */
const getProfile = async (req, res) => {
  try {
    const userId = req.userId;

    // Trova l'utente
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'Utente non trovato' });
    }

    // Risposta
    res.json({
      user: {
        id: user._id,
        email: user.email,
        gender: user.gender,
        credits: user.credits,
        freeClicks: user.freeClicks,
        premium: user.premium,
        verified: user.verified,
        lastLogin: user.lastLogin,
        createdAt: user.createdAt,
        profile: user.profile
      }
    });
  } catch (error) {
    userLogger.error('Errore recupero profilo:', error);
    res.status(500).json({ message: 'Errore del server durante il recupero del profilo' });
  }
};

/**
 * Aggiorna il profilo dell'utente
 * @param {Object} req - Richiesta Express
 * @param {Object} res - Risposta Express
 * @returns {Object} Risposta JSON
 */
const updateProfile = async (req, res) => {
  try {
    // Verifica errori di validazione
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const userId = req.userId;
    const { displayName, bio, preferences } = req.body;

    // Trova l'utente
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'Utente non trovato' });
    }

    // Inizializza il profilo se non esiste
    if (!user.profile) {
      user.profile = {};
    }

    // Aggiorna i campi del profilo
    if (displayName !== undefined) {
      user.profile.displayName = displayName;
    }

    if (bio !== undefined) {
      user.profile.bio = bio;
    }

    // Aggiorna le preferenze
    if (preferences) {
      if (!user.profile.preferences) {
        user.profile.preferences = {};
      }

      // Preferenze di notifica
      if (preferences.notifications) {
        if (!user.profile.preferences.notifications) {
          user.profile.preferences.notifications = {};
        }

        if (preferences.notifications.email !== undefined) {
          user.profile.preferences.notifications.email = preferences.notifications.email;
        }

        if (preferences.notifications.push !== undefined) {
          user.profile.preferences.notifications.push = preferences.notifications.push;
        }
      }

      // Preferenze di privacy
      if (preferences.privacy) {
        if (!user.profile.preferences.privacy) {
          user.profile.preferences.privacy = {};
        }

        if (preferences.privacy.showOnlineStatus !== undefined) {
          user.profile.preferences.privacy.showOnlineStatus = preferences.privacy.showOnlineStatus;
        }

        if (preferences.privacy.allowDirectMessages !== undefined) {
          user.profile.preferences.privacy.allowDirectMessages = preferences.privacy.allowDirectMessages;
        }
      }
    }

    await user.save();

    userLogger.info(`Profilo aggiornato: ${user._id}`);

    // Risposta
    res.json({
      message: 'Profilo aggiornato',
      profile: user.profile
    });
  } catch (error) {
    userLogger.error('Errore aggiornamento profilo:', error);
    res.status(500).json({ message: 'Errore del server durante l\'aggiornamento del profilo' });
  }
};

/**
 * Aggiorna la password dell'utente
 * @param {Object} req - Richiesta Express
 * @param {Object} res - Risposta Express
 * @returns {Object} Risposta JSON
 */
const updatePassword = async (req, res) => {
  try {
    // Verifica errori di validazione
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const userId = req.userId;
    const { currentPassword, newPassword } = req.body;

    // Trova l'utente
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'Utente non trovato' });
    }

    // Verifica la password corrente
    const isMatch = await user.comparePassword(currentPassword);
    if (!isMatch) {
      return res.status(401).json({ message: 'Password corrente non valida' });
    }

    // Aggiorna la password
    user.password = newPassword;
    await user.save();

    userLogger.info(`Password aggiornata: ${user._id}`);

    // Risposta
    res.json({ message: 'Password aggiornata' });
  } catch (error) {
    userLogger.error('Errore aggiornamento password:', error);
    res.status(500).json({ message: 'Errore del server durante l\'aggiornamento della password' });
  }
};

/**
 * Ottiene il bilancio dei crediti dell'utente
 * @param {Object} req - Richiesta Express
 * @param {Object} res - Risposta Express
 * @returns {Object} Risposta JSON
 */
const getCreditBalance = async (req, res) => {
  try {
    const userId = req.userId;

    // Ottieni il bilancio
    const balance = await paymentService.getUserBalance(userId);

    // Risposta
    res.json({ balance });
  } catch (error) {
    userLogger.error('Errore recupero bilancio crediti:', error);
    res.status(500).json({ message: 'Errore del server durante il recupero del bilancio crediti' });
  }
};

/**
 * Ottiene le transazioni dell'utente
 * @param {Object} req - Richiesta Express
 * @param {Object} res - Risposta Express
 * @returns {Object} Risposta JSON
 */
const getTransactions = async (req, res) => {
  try {
    const userId = req.userId;
    const { type, status, limit = 10, skip = 0 } = req.query;

    // Ottieni le transazioni
    const transactions = await paymentService.getUserTransactions(userId, {
      type,
      status,
      limit: parseInt(limit),
      skip: parseInt(skip)
    });

    // Conta il totale
    const total = await Transaction.countDocuments({
      userId,
      ...(type ? { type } : {}),
      ...(status ? { status } : {})
    });

    // Risposta
    res.json({
      transactions,
      total,
      limit: parseInt(limit),
      skip: parseInt(skip)
    });
  } catch (error) {
    userLogger.error('Errore recupero transazioni:', error);
    res.status(500).json({ message: 'Errore del server durante il recupero delle transazioni' });
  }
};

/**
 * Crea una transazione di acquisto
 * @param {Object} req - Richiesta Express
 * @param {Object} res - Risposta Express
 * @returns {Object} Risposta JSON
 */
const createPurchase = async (req, res) => {
  try {
    // Verifica errori di validazione
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const userId = req.userId;
    const { amount, credits, paymentMethod, currency = 'EUR' } = req.body;

    // Crea la transazione
    const transaction = await paymentService.createPurchaseTransaction({
      userId,
      amount,
      credits,
      currency,
      paymentMethod,
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    });

    userLogger.info(`Transazione di acquisto creata: ${transaction._id}`);

    // Risposta
    res.status(201).json({
      message: 'Transazione di acquisto creata',
      transaction: {
        id: transaction._id,
        amount,
        credits,
        currency,
        status: transaction.status,
        paymentMethod
      }
    });
  } catch (error) {
    userLogger.error('Errore creazione transazione di acquisto:', error);
    res.status(500).json({ message: 'Errore del server durante la creazione della transazione di acquisto' });
  }
};

/**
 * Elimina l'account dell'utente
 * @param {Object} req - Richiesta Express
 * @param {Object} res - Risposta Express
 * @returns {Object} Risposta JSON
 */
const deleteAccount = async (req, res) => {
  try {
    // Verifica errori di validazione
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const userId = req.userId;
    const { password } = req.body;

    // Trova l'utente
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: 'Utente non trovato' });
    }

    // Verifica la password
    const isMatch = await user.comparePassword(password);
    if (!isMatch) {
      return res.status(401).json({ message: 'Password non valida' });
    }

    // Elimina l'utente
    await user.remove();

    userLogger.info(`Account eliminato: ${userId}`);

    // Risposta
    res.json({ message: 'Account eliminato' });
  } catch (error) {
    userLogger.error('Errore eliminazione account:', error);
    res.status(500).json({ message: 'Errore del server durante l\'eliminazione dell\'account' });
  }
};

export {
  getProfile,
  updateProfile,
  updatePassword,
  getCreditBalance,
  getTransactions,
  createPurchase,
  deleteAccount
};
