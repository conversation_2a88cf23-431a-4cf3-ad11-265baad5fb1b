/**
 * Configurazione dell'applicazione Express
 * Configura middleware, sicurezza e altre impostazioni
 */
const express = require('express');
const helmet = require('helmet');
const cors = require('cors');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const path = require('path');
const logger = require('./logger');

/**
 * Configura l'applicazione Express con middleware
 */
const configureExpress = (app) => {
  // Disabilita trust proxy per evitare conflitti rate limiting
  app.set('trust proxy', false);

  // Middleware di sicurezza
  app.use(helmet());

  // Compressione gzip
  app.use(compression());

  // Parsing JSON e URL-encoded
  app.use(express.json());
  app.use(express.urlencoded({ extended: true }));

  // CORS per videochatcouple.com
  const corsOptions = {
    origin: ["https://videochatcouple.com", "http://videochatcouple.com", "https://www.videochatcouple.com"],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
  };

  app.use(cors(corsOptions));

  // Rate limiting temporaneamente disabilitato per risolvere conflitti trust proxy
  // TODO: Riattivare con configurazione corretta
  /*
  const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minuti
    max: 100, // limite di 100 richieste per IP
    message: 'Troppi tentativi di accesso da questo indirizzo IP, riprova più tardi.',
    standardHeaders: true,
    legacyHeaders: false
  });
  app.use(limiter);
  */

  // Servire file statici
  app.use(express.static(path.join(__dirname, '../../public')));

  // Gestione degli errori
  app.use((err, req, res, next) => {
    logger.error(err.stack);
    res.status(500).json({ error: 'Errore interno del server' });
  });

  logger.info('Express configurato con successo');
};

module.exports = {
  configureExpress
};
