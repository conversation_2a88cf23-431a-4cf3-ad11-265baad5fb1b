/**
 * Configurazione della connessione a Redis
 */
import Redis from 'ioredis';
import config from './config.js';
import logger from './logger.js';

// Logger specifico per il modulo Redis
const redisLogger = logger.getModuleLogger('redis');

// Opzioni di connessione
const redisOptions = {
  retryStrategy: (times) => {
    if (times > 5) {
      redisLogger.error('Impossibile connettersi a Redis dopo 5 tentativi, modalità fallback attivata');
      return null; // Abbandona i tentativi dopo 5 fallimenti
    }
    const delay = Math.min(times * 100, 3000);
    redisLogger.info(`Tentativo di riconnessione a Redis in ${delay}ms (tentativo ${times})...`);
    return delay;
  },
  maxRetriesPerRequest: 3,
  enableReadyCheck: true,
  autoResendUnfulfilledCommands: true,
  connectTimeout: 5000,
  disconnectTimeout: 2000,
  keepAlive: 10000
};

// Se è impostata una password, aggiungila alle opzioni
if (config.redis.password) {
  redisOptions.password = config.redis.password;
}

// Definizione della chiave per la lista d'attesa
const waitingListKey = 'waiting_users';

// Crea un mock di Redis per la modalità fallback
const createRedisMock = () => {
  redisLogger.warn('Utilizzando Redis in modalità fallback (memoria locale)');

  // Memoria locale per simulare Redis
  const localStore = {
    [waitingListKey]: []
  };

  return {
    ping: () => Promise.resolve('PONG'),
    quit: () => Promise.resolve('OK'),
    lpush: (key, value) => {
      if (!localStore[key]) localStore[key] = [];
      localStore[key].unshift(value);
      return Promise.resolve(localStore[key].length);
    },
    lrange: (key, start, end) => {
      if (!localStore[key]) localStore[key] = [];
      const list = localStore[key];
      const result = end === -1 ? list.slice(start) : list.slice(start, end + 1);
      return Promise.resolve(result);
    },
    lrem: (key, count, value) => {
      if (!localStore[key]) return Promise.resolve(0);
      const list = localStore[key];
      const initialLength = list.length;

      if (count > 0) {
        // Rimuovi le prime 'count' occorrenze
        let removed = 0;
        for (let i = 0; i < list.length && removed < count; i++) {
          if (list[i] === value) {
            list.splice(i, 1);
            i--;
            removed++;
          }
        }
        return Promise.resolve(removed);
      } else if (count < 0) {
        // Rimuovi le ultime 'count' occorrenze
        let removed = 0;
        for (let i = list.length - 1; i >= 0 && removed < -count; i--) {
          if (list[i] === value) {
            list.splice(i, 1);
            removed++;
          }
        }
        return Promise.resolve(removed);
      } else {
        // Rimuovi tutte le occorrenze
        const newList = list.filter(item => item !== value);
        const removed = initialLength - newList.length;
        localStore[key] = newList;
        return Promise.resolve(removed);
      }
    },
    llen: (key) => {
      if (!localStore[key]) localStore[key] = [];
      return Promise.resolve(localStore[key].length);
    },
    on: () => {} // Metodo fittizio per gli eventi
  };
};

// Tenta di creare l'istanza Redis, con fallback a mock in caso di errore
let redis;
let isRedisMock = false;

try {
  redis = new Redis(config.redis.url, redisOptions);

  // Gestione degli eventi
  redis.on('connect', () => {
    redisLogger.info('Connesso a Redis');
  });

  redis.on('ready', () => {
    redisLogger.info('Redis pronto per l\'uso');
  });

  redis.on('error', (err) => {
    redisLogger.error('Errore Redis:', err);
    if (!isRedisMock) {
      redisLogger.warn('Passaggio a Redis in modalità fallback dopo errore');
      redis = createRedisMock();
      isRedisMock = true;
    }
  });

  redis.on('close', () => {
    redisLogger.warn('Connessione Redis chiusa');
  });

  redis.on('reconnecting', () => {
    redisLogger.info('Tentativo di riconnessione a Redis...');
  });

  redis.on('end', () => {
    redisLogger.warn('Connessione Redis terminata');
    if (!isRedisMock) {
      redisLogger.warn('Passaggio a Redis in modalità fallback dopo disconnessione');
      redis = createRedisMock();
      isRedisMock = true;
    }
  });
} catch (error) {
  redisLogger.error('Errore inizializzazione Redis:', error);
  redis = createRedisMock();
  isRedisMock = true;
}

/**
 * Verifica la connessione a Redis
 * @returns {Promise<boolean>} Promise che si risolve con true se il ping ha successo
 */
const ping = async () => {
  try {
    // Imposta un timeout per il ping
    const pingPromise = redis.ping();
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Redis ping timeout')), 5000)
    );

    const result = await Promise.race([pingPromise, timeoutPromise]);
    return result === 'PONG';
  } catch (error) {
    redisLogger.error('Redis ping failed:', error);
    return false;
  }
};

/**
 * Chiude la connessione a Redis
 * @returns {Promise} Promise che si risolve quando la disconnessione è completata
 */
const disconnect = async () => {
  try {
    await redis.quit();
    redisLogger.info('Disconnesso da Redis');
  } catch (error) {
    redisLogger.error('Errore disconnessione Redis:', error);
  }
};

// Gestione della chiusura dell'applicazione
process.on('SIGINT', async () => {
  await disconnect();
});

/**
 * Aggiunge un utente alla lista d'attesa
 * @param {string} socketId - ID del socket dell'utente
 * @param {string} userId - ID dell'utente
 * @param {string} gender - Genere dell'utente
 * @returns {Promise<number>} Promise che si risolve con la lunghezza della lista dopo l'aggiunta
 */
const addToWaitingList = async (socketId, userId, gender) => {
  const userData = JSON.stringify({ socketId, userId, gender });
  return redis.lpush(waitingListKey, userData);
};

/**
 * Rimuove un utente dalla lista d'attesa
 * @param {string} socketId - ID del socket dell'utente da rimuovere
 * @returns {Promise<number>} Promise che si risolve con il numero di elementi rimossi
 */
const removeFromWaitingList = async (socketId) => {
  const waitingList = await redis.lrange(waitingListKey, 0, -1);
  let removed = 0;

  for (let i = 0; i < waitingList.length; i++) {
    try {
      const userData = JSON.parse(waitingList[i]);
      if (userData.socketId === socketId) {
        const result = await redis.lrem(waitingListKey, 1, waitingList[i]);
        removed += result;
      }
    } catch (error) {
      redisLogger.error(`Errore parsing JSON in removeFromWaitingList: ${error.message}`);
    }
  }

  return removed;
};

/**
 * Trova un partner compatibile nella lista d'attesa
 * @param {string} socketId - ID del socket dell'utente che cerca un partner
 * @param {string} gender - Genere preferito per il partner (opzionale)
 * @returns {Promise<object|null>} Promise che si risolve con i dati del partner o null se non trovato
 */
const findMatch = async (socketId, gender) => {
  const waitingList = await redis.lrange(waitingListKey, 0, -1);

  for (let i = 0; i < waitingList.length; i++) {
    try {
      const userData = JSON.parse(waitingList[i]);
      if (userData.socketId !== socketId &&
          (!gender || gender === 'any' || userData.gender === gender)) {
        await redis.lrem(waitingListKey, 1, waitingList[i]);
        return userData;
      }
    } catch (error) {
      redisLogger.error(`Errore parsing JSON in findMatch: ${error.message}`);
    }
  }

  return null;
};

/**
 * Ottiene il numero di utenti in attesa
 * @returns {Promise<number>} Promise che si risolve con il numero di utenti in attesa
 */
const getWaitingCount = async () => {
  return redis.llen(waitingListKey);
};

export default {
  getClient: () => redis,
  ping,
  disconnect,
  addToWaitingList,
  removeFromWaitingList,
  findMatch,
  getWaitingCount,
  isMockRedis: () => isRedisMock
};
