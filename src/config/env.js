/**
 * Configurazione delle variabili d'ambiente
 * Carica le variabili d'ambiente dal file .env
 */

import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const rootPath = path.resolve(__dirname, '../../');

// Carica le variabili d'ambiente dal file .env
dotenv.config({ path: path.join(rootPath, '.env') });

// Imposta le variabili d'ambiente predefinite se non sono definite
process.env.NODE_ENV = process.env.NODE_ENV || 'development';
process.env.PORT = process.env.PORT || 3001;
process.env.SSL_ENABLED = process.env.SSL_ENABLED || 'false';

// Esporta le variabili d'ambiente per l'uso in altri moduli
export default {
  env: process.env.NODE_ENV,
  port: process.env.PORT,
  ssl: {
    enabled: process.env.SSL_ENABLED === 'true',
    keyPath: process.env.SSL_KEY_PATH,
    certPath: process.env.SSL_CERT_PATH
  }
};
