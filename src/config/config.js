const dotenv = require('dotenv');
dotenv.config();

const config = {
  env: process.env.NODE_ENV || 'development',

  server: {
    port: parseInt(process.env.PORT, 10) || 3001,
    host: process.env.HOST || '0.0.0.0',
    baseUrl: process.env.BASE_URL || 'https://videochatcouple.com',
    corsOrigin: process.env.CORS_ORIGIN || 'https://videochatcouple.com'
  },

  database: {
    uri: process.env.MONGODB_URI || 'mongodb://127.0.0.1:27017/videochatcouple',
    testUri: process.env.MONGODB_TEST_URI || 'mongodb://127.0.0.1:27017/videochatcouple_test',
    options: {
      serverSelectionTimeoutMS: 10000,
      maxPoolSize: 100,
      autoIndex: process.env.NODE_ENV === 'development'
    }
  },

  redis: {
    url: process.env.REDIS_URL || 'redis://localhost:6379',
    password: process.env.REDIS_PASSWORD || ''
  },

  auth: {
    jwtSecret: process.env.JWT_SECRET || 'your_jwt_secret_here_change_in_production',
    jwtExpiresIn: process.env.JWT_EXPIRES_IN || '24h',
    cookieSecret: process.env.COOKIE_SECRET || 'your_cookie_secret_here_change_in_production'
  },

  ssl: {
    enabled: process.env.SSL_ENABLED === 'true',
    keyPath: process.env.SSL_KEY_PATH || './ssl/privkey.pem',
    certPath: process.env.SSL_CERT_PATH || './ssl/fullchain.pem'
  },

  logging: {
    level: process.env.LOG_LEVEL || 'info',
    format: process.env.LOG_FORMAT || 'combined',
    dir: process.env.LOG_DIR || './logs'
  },

  rateLimiting: {
    max: parseInt(process.env.RATE_LIMIT_MAX, 10) || 100,
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS, 10) || 900000,
    login: {
      max: parseInt(process.env.LOGIN_RATE_LIMIT_MAX, 10) || 5,
      windowMs: parseInt(process.env.LOGIN_RATE_LIMIT_WINDOW_MS, 10) || 900000
    }
  },

  email: {
    host: process.env.EMAIL_HOST || 'smtp.example.com',
    port: parseInt(process.env.EMAIL_PORT, 10) || 587,
    secure: process.env.EMAIL_SECURE === 'true',
    auth: {
      user: process.env.EMAIL_USER || '<EMAIL>',
      pass: process.env.EMAIL_PASS || 'your-email-password'
    },
    from: process.env.EMAIL_FROM || '<EMAIL>'
  },

  app: {
    name: process.env.APP_NAME || 'VideoChat Couple',
    domain: process.env.DOMAIN || 'videochatcouple.com',
    publicPath: process.env.PUBLIC_PATH || './public',
    initialCredits: parseFloat(process.env.INITIAL_CREDITS) || 10,
    freeClicks: parseInt(process.env.FREE_CLICKS, 10) || 0
  },

  monitoring: {
    enableMetrics: process.env.ENABLE_METRICS === 'true',
    metricsPath: process.env.METRICS_PATH || '/metrics'
  },

  webrtc: {
    iceServers: [
      { urls: 'stun:stun.l.google.com:19302' },
      { urls: 'stun:stun1.l.google.com:19302' },
      { urls: 'stun:stun2.l.google.com:19302' },
      { urls: 'stun:stun3.l.google.com:19302' },
      { urls: 'stun:stun4.l.google.com:19302' },
      {
        urls: process.env.TURN_SERVER_URL || 'turn:numb.viagenie.ca',
        username: process.env.TURN_SERVER_USERNAME || '<EMAIL>',
        credential: process.env.TURN_SERVER_CREDENTIAL || 'muazkh'
      },
      {
        urls: process.env.TURN_SERVER_URL2 || 'turn:turn.videochatcouple.com:3478',
        username: process.env.TURN_SERVER_USERNAME2 || 'videochatcouple',
        credential: process.env.TURN_SERVER_CREDENTIAL2 || 'turnserver'
      }
    ],
    iceCandidatePoolSize: 10,
    bundlePolicy: 'max-bundle',
    rtcpMuxPolicy: 'require',
    sdpSemantics: 'unified-plan'
  },

  isProd: () => process.env.NODE_ENV === 'production',
  isDev: () => process.env.NODE_ENV === 'development',
  isTest: () => process.env.NODE_ENV === 'test'
};

module.exports = config;
