/**
 * Configurazione del logger dell'applicazione
 * Util<PERSON>za Winston per gestire i log in modo strutturato
 */
import winston from 'winston';
import path from 'path';
import config from './config.js';

// Formato personalizzato per i log
const customFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.splat(),
  winston.format.json()
);

// Formato per i log della console in ambiente di sviluppo
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.printf(
    info => `${info.timestamp} ${info.level}: ${info.message}${info.stack ? '\n' + info.stack : ''}`
  )
);

// Crea il logger con le configurazioni appropriate
const logger = winston.createLogger({
  level: config.logging.level,
  format: customFormat,
  defaultMeta: { service: 'videochatcouple' },
  transports: [
    // Scrive tutti i log con livello 'error' e inferiore nel file error.log
    new winston.transports.File({
      filename: path.join(config.logging.dir, 'error.log'),
      level: 'error'
    }),
    // Scrive tutti i log con livello 'info' e inferiore nel file combined.log
    new winston.transports.File({
      filename: path.join(config.logging.dir, 'combined.log')
    })
  ],
  // Gestione degli errori non catturati
  exceptionHandlers: [
    new winston.transports.File({
      filename: path.join(config.logging.dir, 'exceptions.log')
    })
  ],
  // Gestione delle promise non gestite
  rejectionHandlers: [
    new winston.transports.File({
      filename: path.join(config.logging.dir, 'rejections.log')
    })
  ],
  exitOnError: false // Non terminare l'applicazione in caso di errori di logging
});

// Se non siamo in produzione, aggiungi anche il log sulla console
if (config.isDev()) {
  logger.add(new winston.transports.Console({
    format: consoleFormat,
    handleExceptions: true,
    handleRejections: true
  }));
}

// Funzione di utilità per creare un logger per un modulo specifico
logger.getModuleLogger = function(moduleName) {
  return {
    error: (message, meta) => logger.error(message, { module: moduleName, ...meta }),
    warn: (message, meta) => logger.warn(message, { module: moduleName, ...meta }),
    info: (message, meta) => logger.info(message, { module: moduleName, ...meta }),
    http: (message, meta) => logger.http(message, { module: moduleName, ...meta }),
    verbose: (message, meta) => logger.verbose(message, { module: moduleName, ...meta }),
    debug: (message, meta) => logger.debug(message, { module: moduleName, ...meta }),
    silly: (message, meta) => logger.silly(message, { module: moduleName, ...meta })
  };
};

// Esporta il logger
export default logger;
