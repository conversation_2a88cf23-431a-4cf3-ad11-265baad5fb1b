/**
 * Configurazione Express.js
 * Versione CommonJS compatibile
 */

const express = require('express');
const helmet = require('helmet');
const cors = require('cors');
const compression = require('compression');
const cookieParser = require('cookie-parser');
const rateLimit = require('express-rate-limit');
const { RedisStore } = require('rate-limit-redis');
const morgan = require('morgan');
const path = require('path');

// Importiamo le configurazioni in formato CommonJS
// Nota: questi file dovranno essere convertiti da .js a .cjs o modificati per usare module.exports
const config = require('./config');
const logger = require('./logger');
const redisClient = require('./redis');

const app = express();

// Configurazione logger
app.use(
  config.isDev()
    ? morgan('dev', { stream: { write: message => logger.info(message.trim()) } })
    : morgan('combined', { stream: { write: message => logger.info(message.trim()) } })
);

// Configurazione helmet per la sicurezza
app.use(helmet({
  contentSecurityPolicy: {
    useDefaults: false,
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: [
        "'self'",
        "https://cdn.jsdelivr.net",
        "https://cdn.socket.io",
        "https://js.stripe.com",
        "'unsafe-inline'",
        "https://fonts.googleapis.com",
        "'sha256-4axJXpXZxVULPARLwmYz4fzgJb/mpZcIObxUcxhWWVc='",
        "'sha256-/SpqH0bikxMJ4U8/VIrM8GjDRFT9ZF5wJSUQkOLeLx0='"
      ],
      connectSrc: [
        "'self'",
        config.server.baseUrl,
        `wss://${config.app.domain}`,
        `ws://${config.app.domain}`,
        "https://*.stripe.com",
        "https://fonts.googleapis.com",
        "https://fonts.gstatic.com",
        "https://*.videochatcouple.com",
        "wss://*.videochatcouple.com",
        "ws://*.videochatcouple.com",
        "stun:*.google.com:*",
        "turn:turn.videochatcouple.com:*",
        "turns:turn.videochatcouple.com:*"
      ],
      styleSrc: ["'self'", "https://fonts.googleapis.com", "'unsafe-inline'"],
      fontSrc: ["'self'", "https://fonts.gstatic.com", "data:"],
      imgSrc: ["'self'", "data:", "blob:", "https:"],
      mediaSrc: ["'self'", "blob:", "mediastream:"],
      frameSrc: ["https://js.stripe.com"],
      workerSrc: ["'self'", "blob:"],
      manifestSrc: ["'self'"],
      objectSrc: ["'none'"],
      baseUri: ["'self'"],
      formAction: ["'self'"],
      frameAncestors: ["'none'"],
      upgradeInsecureRequests: [],
      blockAllMixedContent: []
    }
  },
  referrerPolicy: { policy: 'strict-origin-when-cross-origin' },
  crossOriginEmbedderPolicy: { requireCorp: false },
  crossOriginResourcePolicy: { policy: 'cross-origin' },
  crossOriginOpenerPolicy: { policy: 'same-origin-allow-popups' },
  hsts: { maxAge: 31536000, includeSubDomains: true, preload: true },
  frameguard: { action: 'deny' },
  xssFilter: true,
  noSniff: true,
  dnsPrefetchControl: { allow: false },
  expectCt: { maxAge: 86400, enforce: true },
  permittedCrossDomainPolicies: { permittedPolicies: 'none' },
  originAgentCluster: true
}));

// Configurazione CORS
app.use(cors({
  origin: '*', // In produzione, limitare alle origini consentite
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  credentials: true,
  maxAge: 86400
}));

// Middleware per parsing e compressione
app.use(compression());
app.use(express.json({ limit: '1mb' }));
app.use(express.urlencoded({ extended: true, limit: '1mb' }));
app.use(cookieParser());

// Configurazione rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minuti
  max: 100, // limite di richieste per IP
  standardHeaders: true,
  legacyHeaders: false,
  store: new RedisStore({
    sendCommand: (...args) => redisClient.sendCommand(args),
    prefix: 'rl:',
    resetExpiryOnChange: true
  }),
  message: 'Troppe richieste da questo IP, riprova più tardi'
});

// Applica rate limiting a tutte le richieste
app.use(limiter);

// Servire file statici
app.use(express.static(path.join(__dirname, '../../public')));

// Gestione errori 404
app.use((req, res, next) => {
  res.status(404).sendFile(path.join(__dirname, '../../public/404.html'));
});

// Gestione errori generici
app.use((err, req, res, next) => {
  logger.error(`Errore: ${err.message}`);
  res.status(500).sendFile(path.join(__dirname, '../../public/500.html'));
});

// Esportiamo l'app usando module.exports (CommonJS) invece di export default (ES6)
module.exports = app;
