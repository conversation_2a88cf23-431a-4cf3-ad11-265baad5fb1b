/**
 * Test per l'autenticazione
 */
const request = require('supertest');
const mongoose = require('mongoose');
const app = require('../config/express');
const User = require('../models/user.model');
const config = require('../config/config');
const { generateRandomToken } = require('../utils/token');

// Configura il database di test
beforeAll(async () => {
  await mongoose.connect(config.database.testUri, config.database.options);
});

// Pulisci il database dopo i test
afterAll(async () => {
  await User.deleteMany({});
  await mongoose.connection.close();
});

// Pulisci il database prima di ogni test
beforeEach(async () => {
  await User.deleteMany({});
});

describe('Auth API', () => {
  describe('POST /api/auth/register', () => {
    it('should register a new user', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'Password123',
        gender: 'male'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      expect(response.body).toHaveProperty('token');
      expect(response.body).toHaveProperty('user');
      expect(response.body.user.email).toBe(userData.email);
      expect(response.body.user.gender).toBe(userData.gender);
      expect(response.body.user).toHaveProperty('credits');
      expect(response.body.user).toHaveProperty('freeClicks');
      expect(response.body.user).toHaveProperty('premium');
      expect(response.body.user).toHaveProperty('verified');
    });

    it('should return 400 if email is invalid', async () => {
      const userData = {
        email: 'invalid-email',
        password: 'Password123',
        gender: 'male'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(400);

      expect(response.body).toHaveProperty('errors');
    });

    it('should return 400 if password is too short', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'pass',
        gender: 'male'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(400);

      expect(response.body).toHaveProperty('errors');
    });

    it('should return 400 if gender is invalid', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'Password123',
        gender: 'invalid'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(400);

      expect(response.body).toHaveProperty('errors');
    });

    it('should return 400 if email is already registered', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'Password123',
        gender: 'male'
      };

      // Registra l'utente una prima volta
      await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      // Prova a registrare lo stesso utente una seconda volta
      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(400);

      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('già registrata');
    });
  });

  describe('POST /api/auth/login', () => {
    it('should login a user', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'Password123',
        gender: 'male'
      };

      // Registra l'utente
      await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      // Effettua il login
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: userData.email,
          password: userData.password
        })
        .expect(200);

      expect(response.body).toHaveProperty('token');
      expect(response.body).toHaveProperty('user');
      expect(response.body.user.email).toBe(userData.email);
    });

    it('should return 401 if credentials are invalid', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'Password123',
        gender: 'male'
      };

      // Registra l'utente
      await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      // Prova a effettuare il login con password errata
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: userData.email,
          password: 'WrongPassword123'
        })
        .expect(401);

      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('non valide');
    });
  });

  describe('GET /api/auth/verify/:token', () => {
    it('should verify a user account', async () => {
      // Crea un utente con un token di verifica
      const user = new User({
        email: '<EMAIL>',
        password: 'Password123',
        gender: 'male',
        verificationToken: generateRandomToken(),
        verificationExpires: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 ore
      });

      await user.save();

      // Verifica l'account
      const response = await request(app)
        .get(`/api/auth/verify/${user.verificationToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('verificato');

      // Verifica che l'utente sia stato aggiornato nel database
      const updatedUser = await User.findById(user._id);
      expect(updatedUser.verified).toBe(true);
      expect(updatedUser.verificationToken).toBeUndefined();
      expect(updatedUser.verificationExpires).toBeUndefined();
    });

    it('should return 400 if token is invalid', async () => {
      const response = await request(app)
        .get('/api/auth/verify/invalid-token')
        .expect(400);

      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('non valido');
    });
  });

  describe('GET /api/auth/profile', () => {
    it('should return user profile if authenticated', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'Password123',
        gender: 'male'
      };

      // Registra l'utente
      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      const token = registerResponse.body.token;

      // Ottieni il profilo
      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      expect(response.body).toHaveProperty('user');
      expect(response.body.user.email).toBe(userData.email);
    });

    it('should return 401 if not authenticated', async () => {
      const response = await request(app)
        .get('/api/auth/profile')
        .expect(401);

      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('Autenticazione');
    });
  });
});
