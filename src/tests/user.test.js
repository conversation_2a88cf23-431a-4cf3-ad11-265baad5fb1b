/**
 * Test per gli utenti
 */
const request = require('supertest');
const mongoose = require('mongoose');
const app = require('../config/express');
const User = require('../models/user.model');
const Transaction = require('../models/transaction.model');
const config = require('../config/config');
const { generateJwtToken } = require('../utils/token');

// Configura il database di test
beforeAll(async () => {
  await mongoose.connect(config.database.testUri, config.database.options);
});

// Pulisci il database dopo i test
afterAll(async () => {
  await User.deleteMany({});
  await Transaction.deleteMany({});
  await mongoose.connection.close();
});

// Pulisci il database prima di ogni test
beforeEach(async () => {
  await User.deleteMany({});
  await Transaction.deleteMany({});
});

describe('User API', () => {
  let user, token;

  // Crea un utente per i test
  beforeEach(async () => {
    user = new User({
      email: '<EMAIL>',
      password: 'Password123',
      gender: 'male',
      verified: true,
      credits: 10
    });
    await user.save();
    token = generateJwtToken({ id: user._id, email: user.email });
  });

  describe('GET /api/users/profile', () => {
    it('should get user profile', async () => {
      const response = await request(app)
        .get('/api/users/profile')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      expect(response.body).toHaveProperty('user');
      expect(response.body.user.email).toBe(user.email);
      expect(response.body.user.gender).toBe(user.gender);
      expect(response.body.user.credits).toBe(user.credits);
      expect(response.body.user.verified).toBe(user.verified);
    });

    it('should return 401 if not authenticated', async () => {
      const response = await request(app)
        .get('/api/users/profile')
        .expect(401);

      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('Autenticazione');
    });
  });

  describe('PUT /api/users/profile', () => {
    it('should update user profile', async () => {
      const profileData = {
        displayName: 'Test User',
        bio: 'This is a test bio',
        preferences: {
          notifications: {
            email: false,
            push: true
          },
          privacy: {
            showOnlineStatus: false,
            allowDirectMessages: true
          }
        }
      };

      const response = await request(app)
        .put('/api/users/profile')
        .set('Authorization', `Bearer ${token}`)
        .send(profileData)
        .expect(200);

      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('profile');
      expect(response.body.profile.displayName).toBe(profileData.displayName);
      expect(response.body.profile.bio).toBe(profileData.bio);
      expect(response.body.profile.preferences.notifications.email).toBe(profileData.preferences.notifications.email);
      expect(response.body.profile.preferences.notifications.push).toBe(profileData.preferences.notifications.push);
      expect(response.body.profile.preferences.privacy.showOnlineStatus).toBe(profileData.preferences.privacy.showOnlineStatus);
      expect(response.body.profile.preferences.privacy.allowDirectMessages).toBe(profileData.preferences.privacy.allowDirectMessages);

      // Verifica che l'utente sia stato aggiornato nel database
      const updatedUser = await User.findById(user._id);
      expect(updatedUser.profile.displayName).toBe(profileData.displayName);
      expect(updatedUser.profile.bio).toBe(profileData.bio);
    });

    it('should return 400 if profile data is invalid', async () => {
      const profileData = {
        displayName: 'A', // Nome troppo corto
        bio: 'This is a test bio'
      };

      const response = await request(app)
        .put('/api/users/profile')
        .set('Authorization', `Bearer ${token}`)
        .send(profileData)
        .expect(400);

      expect(response.body).toHaveProperty('errors');
    });
  });

  describe('PUT /api/users/password', () => {
    it('should update user password', async () => {
      const passwordData = {
        currentPassword: 'Password123',
        newPassword: 'NewPassword123'
      };

      const response = await request(app)
        .put('/api/users/password')
        .set('Authorization', `Bearer ${token}`)
        .send(passwordData)
        .expect(200);

      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('Password aggiornata');

      // Verifica che la password sia stata aggiornata nel database
      const updatedUser = await User.findById(user._id);
      const isMatch = await updatedUser.comparePassword(passwordData.newPassword);
      expect(isMatch).toBe(true);
    });

    it('should return 401 if current password is invalid', async () => {
      const passwordData = {
        currentPassword: 'WrongPassword123',
        newPassword: 'NewPassword123'
      };

      const response = await request(app)
        .put('/api/users/password')
        .set('Authorization', `Bearer ${token}`)
        .send(passwordData)
        .expect(401);

      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('non valida');
    });

    it('should return 400 if new password is invalid', async () => {
      const passwordData = {
        currentPassword: 'Password123',
        newPassword: 'weak' // Password troppo debole
      };

      const response = await request(app)
        .put('/api/users/password')
        .set('Authorization', `Bearer ${token}`)
        .send(passwordData)
        .expect(400);

      expect(response.body).toHaveProperty('errors');
    });
  });

  describe('GET /api/users/credits', () => {
    it('should get user credit balance', async () => {
      // Crea alcune transazioni per l'utente
      await Transaction.create({
        userId: user._id,
        type: 'purchase',
        amount: 10,
        credits: 20,
        status: 'completed'
      });

      await Transaction.create({
        userId: user._id,
        type: 'usage',
        amount: 0,
        credits: 5,
        status: 'completed'
      });

      const response = await request(app)
        .get('/api/users/credits')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      expect(response.body).toHaveProperty('balance');
      expect(response.body.balance).toHaveProperty('purchased');
      expect(response.body.balance).toHaveProperty('used');
      expect(response.body.balance).toHaveProperty('balance');
      expect(response.body.balance).toHaveProperty('currentCredits');
      expect(response.body.balance.purchased).toBe(20);
      expect(response.body.balance.used).toBe(5);
      expect(response.body.balance.currentCredits).toBe(10);
    });
  });

  describe('GET /api/users/transactions', () => {
    beforeEach(async () => {
      // Crea alcune transazioni per l'utente
      await Transaction.insertMany([
        {
          userId: user._id,
          type: 'purchase',
          amount: 10,
          credits: 20,
          status: 'completed',
          createdAt: new Date(Date.now() - 3600000)
        },
        {
          userId: user._id,
          type: 'usage',
          amount: 0,
          credits: 5,
          status: 'completed',
          createdAt: new Date(Date.now() - 1800000)
        },
        {
          userId: user._id,
          type: 'bonus',
          amount: 0,
          credits: 10,
          status: 'completed',
          createdAt: new Date()
        }
      ]);
    });

    it('should get user transactions', async () => {
      const response = await request(app)
        .get('/api/users/transactions')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      expect(response.body).toHaveProperty('transactions');
      expect(response.body).toHaveProperty('total');
      expect(response.body.transactions.length).toBe(3);
      expect(response.body.total).toBe(3);
    });

    it('should filter transactions by type', async () => {
      const response = await request(app)
        .get('/api/users/transactions?type=purchase')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      expect(response.body).toHaveProperty('transactions');
      expect(response.body).toHaveProperty('total');
      expect(response.body.transactions.length).toBe(1);
      expect(response.body.total).toBe(1);
      expect(response.body.transactions[0].type).toBe('purchase');
    });

    it('should limit and skip transactions', async () => {
      const response = await request(app)
        .get('/api/users/transactions?limit=1&skip=1')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      expect(response.body).toHaveProperty('transactions');
      expect(response.body).toHaveProperty('total');
      expect(response.body.transactions.length).toBe(1);
      expect(response.body.total).toBe(3);
      expect(response.body.limit).toBe(1);
      expect(response.body.skip).toBe(1);
    });
  });

  describe('POST /api/users/purchases', () => {
    it('should create a purchase transaction', async () => {
      const purchaseData = {
        amount: 10,
        credits: 20,
        paymentMethod: 'credit_card'
      };

      const response = await request(app)
        .post('/api/users/purchases')
        .set('Authorization', `Bearer ${token}`)
        .send(purchaseData)
        .expect(201);

      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('transaction');
      expect(response.body.transaction.amount).toBe(purchaseData.amount);
      expect(response.body.transaction.credits).toBe(purchaseData.credits);
      expect(response.body.transaction.paymentMethod).toBe(purchaseData.paymentMethod);
      expect(response.body.transaction.status).toBe('pending');

      // Verifica che la transazione sia stata creata nel database
      const transaction = await Transaction.findById(response.body.transaction.id);
      expect(transaction).toBeTruthy();
      expect(transaction.userId.toString()).toBe(user._id.toString());
      expect(transaction.type).toBe('purchase');
      expect(transaction.amount).toBe(purchaseData.amount);
      expect(transaction.credits).toBe(purchaseData.credits);
      expect(transaction.paymentMethod).toBe(purchaseData.paymentMethod);
      expect(transaction.status).toBe('pending');
    });

    it('should return 400 if purchase data is invalid', async () => {
      const purchaseData = {
        amount: -10, // Importo non valido
        credits: 20,
        paymentMethod: 'credit_card'
      };

      const response = await request(app)
        .post('/api/users/purchases')
        .set('Authorization', `Bearer ${token}`)
        .send(purchaseData)
        .expect(400);

      expect(response.body).toHaveProperty('errors');
    });
  });
});
