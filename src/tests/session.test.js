/**
 * Test per le sessioni
 */
const request = require('supertest');
const mongoose = require('mongoose');
const app = require('../config/express');
const User = require('../models/user.model');
const Session = require('../models/session.model');
const config = require('../config/config');
const { generateJwtToken } = require('../utils/token');

// Configura il database di test
beforeAll(async () => {
  await mongoose.connect(config.database.testUri, config.database.options);
});

// Pulisci il database dopo i test
afterAll(async () => {
  await User.deleteMany({});
  await Session.deleteMany({});
  await mongoose.connection.close();
});

// Pulisci il database prima di ogni test
beforeEach(async () => {
  await User.deleteMany({});
  await Session.deleteMany({});
});

describe('Session API', () => {
  let user1, user2, token1, token2;

  // Crea due utenti per i test
  beforeEach(async () => {
    // Crea il primo utente
    user1 = new User({
      email: '<EMAIL>',
      password: 'Password123',
      gender: 'male',
      verified: true,
      credits: 10
    });
    await user1.save();
    token1 = generateJwtToken({ id: user1._id, email: user1.email });

    // Crea il secondo utente
    user2 = new User({
      email: '<EMAIL>',
      password: 'Password123',
      gender: 'female',
      verified: true,
      credits: 10
    });
    await user2.save();
    token2 = generateJwtToken({ id: user2._id, email: user2.email });
  });

  describe('POST /api/sessions', () => {
    it('should create a new session', async () => {
      const response = await request(app)
        .post('/api/sessions')
        .set('Authorization', `Bearer ${token1}`)
        .send({ partnerId: user2._id })
        .expect(201);

      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('session');
      expect(response.body.session).toHaveProperty('id');
      expect(response.body.session).toHaveProperty('startTime');
      expect(response.body.session.status).toBe('active');

      // Verifica che la sessione sia stata creata nel database
      const session = await Session.findById(response.body.session.id);
      expect(session).toBeTruthy();
      expect(session.userId.toString()).toBe(user1._id.toString());
      expect(session.partnerId.toString()).toBe(user2._id.toString());
      expect(session.status).toBe('active');

      // Verifica che i crediti dell'utente siano stati detratti
      const updatedUser = await User.findById(user1._id);
      expect(updatedUser.credits).toBe(9);
    });

    it('should return 404 if partner does not exist', async () => {
      const nonExistentId = new mongoose.Types.ObjectId();

      const response = await request(app)
        .post('/api/sessions')
        .set('Authorization', `Bearer ${token1}`)
        .send({ partnerId: nonExistentId })
        .expect(404);

      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('non trovato');
    });

    it('should return 402 if user has insufficient credits', async () => {
      // Imposta i crediti dell'utente a 0
      await User.findByIdAndUpdate(user1._id, { credits: 0 });

      const response = await request(app)
        .post('/api/sessions')
        .set('Authorization', `Bearer ${token1}`)
        .send({ partnerId: user2._id })
        .expect(402);

      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('insufficienti');
    });

    it('should return 401 if not authenticated', async () => {
      const response = await request(app)
        .post('/api/sessions')
        .send({ partnerId: user2._id })
        .expect(401);

      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('Autenticazione');
    });
  });

  describe('PUT /api/sessions/:sessionId/end', () => {
    let session;

    beforeEach(async () => {
      // Crea una sessione attiva
      session = new Session({
        userId: user1._id,
        partnerId: user2._id,
        status: 'active'
      });
      await session.save();
    });

    it('should end a session', async () => {
      const response = await request(app)
        .put(`/api/sessions/${session._id}/end`)
        .set('Authorization', `Bearer ${token1}`)
        .expect(200);

      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('session');
      expect(response.body.session.status).toBe('ended');
      expect(response.body.session).toHaveProperty('endTime');
      expect(response.body.session).toHaveProperty('duration');

      // Verifica che la sessione sia stata aggiornata nel database
      const updatedSession = await Session.findById(session._id);
      expect(updatedSession.status).toBe('ended');
      expect(updatedSession.endTime).toBeTruthy();
      expect(updatedSession.duration).toBeGreaterThanOrEqual(0);
    });

    it('should return 404 if session does not exist', async () => {
      const nonExistentId = new mongoose.Types.ObjectId();

      const response = await request(app)
        .put(`/api/sessions/${nonExistentId}/end`)
        .set('Authorization', `Bearer ${token1}`)
        .expect(404);

      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('non trovata');
    });

    it('should return 403 if user is not authorized', async () => {
      // Crea un terzo utente non coinvolto nella sessione
      const user3 = new User({
        email: '<EMAIL>',
        password: 'Password123',
        gender: 'male',
        verified: true
      });
      await user3.save();
      const token3 = generateJwtToken({ id: user3._id, email: user3.email });

      const response = await request(app)
        .put(`/api/sessions/${session._id}/end`)
        .set('Authorization', `Bearer ${token3}`)
        .expect(403);

      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('Non autorizzato');
    });

    it('should return 400 if session is already ended', async () => {
      // Termina la sessione
      session.status = 'ended';
      session.endTime = new Date();
      await session.save();

      const response = await request(app)
        .put(`/api/sessions/${session._id}/end`)
        .set('Authorization', `Bearer ${token1}`)
        .expect(400);

      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('già terminata');
    });
  });

  describe('POST /api/sessions/:sessionId/rate', () => {
    let session;

    beforeEach(async () => {
      // Crea una sessione terminata
      session = new Session({
        userId: user1._id,
        partnerId: user2._id,
        status: 'ended',
        endTime: new Date(),
        duration: 300
      });
      await session.save();
    });

    it('should rate a session', async () => {
      const ratingData = {
        rating: 5,
        feedback: 'Great session!'
      };

      const response = await request(app)
        .post(`/api/sessions/${session._id}/rate`)
        .set('Authorization', `Bearer ${token1}`)
        .send(ratingData)
        .expect(200);

      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('session');
      expect(response.body.session.userRating).toBe(ratingData.rating);

      // Verifica che la sessione sia stata aggiornata nel database
      const updatedSession = await Session.findById(session._id);
      expect(updatedSession.userRating).toBe(ratingData.rating);
      expect(updatedSession.userFeedback).toBe(ratingData.feedback);
    });

    it('should return 400 if rating is invalid', async () => {
      const ratingData = {
        rating: 6, // Valutazione non valida (deve essere tra 1 e 5)
        feedback: 'Great session!'
      };

      const response = await request(app)
        .post(`/api/sessions/${session._id}/rate`)
        .set('Authorization', `Bearer ${token1}`)
        .send(ratingData)
        .expect(400);

      expect(response.body).toHaveProperty('errors');
    });

    it('should return 400 if session is active', async () => {
      // Imposta la sessione come attiva
      session.status = 'active';
      session.endTime = undefined;
      await session.save();

      const ratingData = {
        rating: 5,
        feedback: 'Great session!'
      };

      const response = await request(app)
        .post(`/api/sessions/${session._id}/rate`)
        .set('Authorization', `Bearer ${token1}`)
        .send(ratingData)
        .expect(400);

      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('non puoi valutare');
    });
  });

  describe('GET /api/sessions', () => {
    beforeEach(async () => {
      // Crea alcune sessioni per l'utente 1
      const sessions = [
        {
          userId: user1._id,
          partnerId: user2._id,
          status: 'active',
          startTime: new Date()
        },
        {
          userId: user1._id,
          partnerId: user2._id,
          status: 'ended',
          startTime: new Date(Date.now() - 3600000),
          endTime: new Date(),
          duration: 3600
        },
        {
          userId: user1._id,
          partnerId: user2._id,
          status: 'interrupted',
          startTime: new Date(Date.now() - 7200000),
          endTime: new Date(Date.now() - 3600000),
          duration: 3600
        }
      ];

      await Session.insertMany(sessions);
    });

    it('should get user sessions', async () => {
      const response = await request(app)
        .get('/api/sessions')
        .set('Authorization', `Bearer ${token1}`)
        .expect(200);

      expect(response.body).toHaveProperty('sessions');
      expect(response.body).toHaveProperty('total');
      expect(response.body.sessions.length).toBe(3);
      expect(response.body.total).toBe(3);
    });

    it('should filter sessions by status', async () => {
      const response = await request(app)
        .get('/api/sessions?status=active')
        .set('Authorization', `Bearer ${token1}`)
        .expect(200);

      expect(response.body).toHaveProperty('sessions');
      expect(response.body).toHaveProperty('total');
      expect(response.body.sessions.length).toBe(1);
      expect(response.body.total).toBe(1);
      expect(response.body.sessions[0].status).toBe('active');
    });

    it('should limit and skip sessions', async () => {
      const response = await request(app)
        .get('/api/sessions?limit=1&skip=1')
        .set('Authorization', `Bearer ${token1}`)
        .expect(200);

      expect(response.body).toHaveProperty('sessions');
      expect(response.body).toHaveProperty('total');
      expect(response.body.sessions.length).toBe(1);
      expect(response.body.total).toBe(3);
      expect(response.body.limit).toBe(1);
      expect(response.body.skip).toBe(1);
    });
  });
});
