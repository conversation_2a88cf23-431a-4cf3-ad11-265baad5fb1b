/**
 * <PERSON><PERSON><PERSON> per l'invio di notifiche
 */
import config from '../config/config.js';
import logger from '../config/logging/logger.js';
import * as emailService from './email.service.js';
import User from '../models/user.model.js';

// Logger specifico per il modulo notifiche
const notificationLogger = logger.getModuleLogger('notification');

/**
 * Invia una notifica
 * @param {string} userId - ID dell'utente
 * @param {Object} notification - <PERSON><PERSON> della notifica
 * @returns {Promise<boolean>} Promise che si risolve con true se la notifica è stata inviata
 */
const sendNotification = async (userId, notification) => {
  try {
    // Trova l'utente
    const user = await User.findById(userId);
    if (!user) {
      notificationLogger.warn(`Utente non trovato: ${userId}`);
      return false;
    }

    // Verifica le preferenze dell'utente
    const preferences = user.profile?.preferences?.notifications || { email: true, push: true };

    // Invia notifica via email
    if (preferences.email && notification.email) {
      await sendEmailNotification(user, notification);
    }

    // Invia notifica push
    if (preferences.push && notification.push) {
      await sendPushNotification(user, notification);
    }

    // Invia notifica in-app
    await saveInAppNotification(user, notification);

    return true;
  } catch (error) {
    notificationLogger.error('Errore invio notifica:', error);
    return false;
  }
};

/**
 * Invia una notifica via email
 * @param {Object} user - Utente
 * @param {Object} notification - Dati della notifica
 * @returns {Promise<void>}
 */
const sendEmailNotification = async (user, notification) => {
  try {
    const emailOptions = {
      to: user.email,
      subject: notification.email.subject || notification.title,
      text: notification.email.text || notification.body,
      html: notification.email.html || `<p>${notification.body}</p>`
    };

    await emailService.sendEmail(emailOptions);
    notificationLogger.info(`Notifica email inviata a ${user.email}`);
  } catch (error) {
    notificationLogger.error('Errore invio notifica email:', error);
    throw error;
  }
};

/**
 * Invia una notifica push
 * @param {Object} user - Utente
 * @param {Object} notification - Dati della notifica
 * @returns {Promise<void>}
 */
const sendPushNotification = async (user, notification) => {
  try {
    // Implementazione della notifica push
    // Questo è un esempio di come potrebbe essere implementato
    // In una implementazione reale, dovresti utilizzare un servizio come Firebase Cloud Messaging

    notificationLogger.info(`Notifica push inviata a ${user.email}`);
  } catch (error) {
    notificationLogger.error('Errore invio notifica push:', error);
    throw error;
  }
};

/**
 * Salva una notifica in-app
 * @param {Object} user - Utente
 * @param {Object} notification - Dati della notifica
 * @returns {Promise<void>}
 */
const saveInAppNotification = async (user, notification) => {
  try {
    // Implementazione della notifica in-app
    // Questo è un esempio di come potrebbe essere implementato
    // In una implementazione reale, dovresti salvare la notifica nel database

    notificationLogger.info(`Notifica in-app salvata per ${user.email}`);
  } catch (error) {
    notificationLogger.error('Errore salvataggio notifica in-app:', error);
    throw error;
  }
};

/**
 * Invia una notifica di benvenuto
 * @param {string} userId - ID dell'utente
 * @returns {Promise<boolean>} Promise che si risolve con true se la notifica è stata inviata
 */
const sendWelcomeNotification = async (userId) => {
  const notification = {
    title: `Benvenuto su ${config.app.name}!`,
    body: `Grazie per esserti registrato su ${config.app.name}. Hai ricevuto ${config.app.initialCredits} crediti gratuiti per iniziare.`,
    type: 'welcome',
    email: {
      subject: `Benvenuto su ${config.app.name}!`,
      text: `
        Ciao,

        Grazie per esserti registrato su ${config.app.name}!

        Hai ricevuto ${config.app.initialCredits} crediti gratuiti per iniziare a utilizzare la piattaforma.

        Ecco alcune informazioni utili:
        - Puoi utilizzare i crediti per connetterti con persone da tutto il mondo
        - La tua privacy e sicurezza sono la nostra priorità
        - Puoi acquistare altri crediti in qualsiasi momento

        Se hai domande, non esitare a contattarci.

        Cordiali saluti,
        Il team di ${config.app.name}
      `,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
          <h2 style="color: #3498db;">Benvenuto su ${config.app.name}!</h2>
          <p>Ciao,</p>
          <p>Grazie per esserti registrato su ${config.app.name}!</p>
          <p>Hai ricevuto <strong>${config.app.initialCredits} crediti gratuiti</strong> per iniziare a utilizzare la piattaforma.</p>
          <p>Ecco alcune informazioni utili:</p>
          <ul>
            <li>Puoi utilizzare i crediti per connetterti con persone da tutto il mondo</li>
            <li>La tua privacy e sicurezza sono la nostra priorità</li>
            <li>Puoi acquistare altri crediti in qualsiasi momento</li>
          </ul>
          <p>Se hai domande, non esitare a contattarci.</p>
          <p>Cordiali saluti,<br>Il team di ${config.app.name}</p>
        </div>
      `
    },
    push: {
      title: `Benvenuto su ${config.app.name}!`,
      body: `Hai ricevuto ${config.app.initialCredits} crediti gratuiti per iniziare.`,
      icon: '/android-chrome-192x192.png',
      url: '/'
    }
  };

  return sendNotification(userId, notification);
};

/**
 * Invia una notifica di acquisto crediti
 * @param {string} userId - ID dell'utente
 * @param {number} credits - Numero di crediti acquistati
 * @param {number} amount - Importo pagato
 * @param {string} currency - Valuta
 * @returns {Promise<boolean>} Promise che si risolve con true se la notifica è stata inviata
 */
const sendCreditPurchaseNotification = async (userId, credits, amount, currency = 'EUR') => {
  const notification = {
    title: 'Acquisto crediti completato',
    body: `Hai acquistato ${credits} crediti per ${amount} ${currency}.`,
    type: 'credit_purchase',
    email: {
      subject: 'Acquisto crediti completato',
      text: `
        Ciao,

        Grazie per il tuo acquisto su ${config.app.name}!

        Dettagli dell'acquisto:
        - Crediti: ${credits}
        - Importo: ${amount} ${currency}
        - Data: ${new Date().toLocaleDateString()}

        I crediti sono stati aggiunti al tuo account e sono disponibili per l'uso immediato.

        Cordiali saluti,
        Il team di ${config.app.name}
      `,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
          <h2 style="color: #3498db;">Acquisto crediti completato</h2>
          <p>Ciao,</p>
          <p>Grazie per il tuo acquisto su ${config.app.name}!</p>
          <p>Dettagli dell'acquisto:</p>
          <ul>
            <li>Crediti: <strong>${credits}</strong></li>
            <li>Importo: <strong>${amount} ${currency}</strong></li>
            <li>Data: <strong>${new Date().toLocaleDateString()}</strong></li>
          </ul>
          <p>I crediti sono stati aggiunti al tuo account e sono disponibili per l'uso immediato.</p>
          <p>Cordiali saluti,<br>Il team di ${config.app.name}</p>
        </div>
      `
    },
    push: {
      title: 'Acquisto crediti completato',
      body: `Hai acquistato ${credits} crediti per ${amount} ${currency}.`,
      icon: '/android-chrome-192x192.png',
      url: '/profile'
    }
  };

  return sendNotification(userId, notification);
};

/**
 * Invia una notifica di crediti in esaurimento
 * @param {string} userId - ID dell'utente
 * @param {number} credits - Numero di crediti rimanenti
 * @returns {Promise<boolean>} Promise che si risolve con true se la notifica è stata inviata
 */
const sendLowCreditsNotification = async (userId, credits) => {
  const notification = {
    title: 'Crediti in esaurimento',
    body: `Hai solo ${credits} crediti rimanenti. Acquista altri crediti per continuare a utilizzare la piattaforma.`,
    type: 'low_credits',
    email: {
      subject: 'Crediti in esaurimento',
      text: `
        Ciao,

        Ti informiamo che hai solo ${credits} crediti rimanenti sul tuo account ${config.app.name}.

        Per continuare a utilizzare la piattaforma senza interruzioni, ti consigliamo di acquistare altri crediti.

        Puoi acquistare crediti in qualsiasi momento dalla sezione "Acquista crediti" del tuo profilo.

        Cordiali saluti,
        Il team di ${config.app.name}
      `,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
          <h2 style="color: #e74c3c;">Crediti in esaurimento</h2>
          <p>Ciao,</p>
          <p>Ti informiamo che hai solo <strong>${credits} crediti</strong> rimanenti sul tuo account ${config.app.name}.</p>
          <p>Per continuare a utilizzare la piattaforma senza interruzioni, ti consigliamo di acquistare altri crediti.</p>
          <p>Puoi acquistare crediti in qualsiasi momento dalla sezione "Acquista crediti" del tuo profilo.</p>
          <p style="text-align: center; margin-top: 20px;">
            <a href="${config.server.baseUrl}/profile/credits" style="display: inline-block; padding: 10px 20px; background-color: #3498db; color: white; text-decoration: none; border-radius: 5px;">Acquista crediti</a>
          </p>
          <p>Cordiali saluti,<br>Il team di ${config.app.name}</p>
        </div>
      `
    },
    push: {
      title: 'Crediti in esaurimento',
      body: `Hai solo ${credits} crediti rimanenti. Acquista altri crediti per continuare.`,
      icon: '/android-chrome-192x192.png',
      url: '/profile/credits'
    }
  };

  return sendNotification(userId, notification);
};

export {
  sendNotification,
  sendWelcomeNotification,
  sendCreditPurchaseNotification,
  sendLowCreditsNotification
};
