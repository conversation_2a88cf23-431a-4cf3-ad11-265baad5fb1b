const nodemailer = require('nodemailer');
const logger = require('../utils/logger');

class EmailService {
  constructor() {
    this.transporter = nodemailer.createTransport({
      service: 'hotmail',
      auth: {
        user: '<EMAIL>',
        pass: 'Leonida1993!'
      },
      tls: {
        rejectUnauthorized: false
      }
    });
    
    // Verifica configurazione
    this.verifyConnection();
  }

  async verifyConnection() {
    try {
      await this.transporter.verify();
      logger.info('✅ Servizio email configurato correttamente');
    } catch (error) {
      logger.error('❌ Errore configurazione email:', error);
    }
  }

  // Template email di benvenuto
  getWelcomeEmailTemplate(userEmail) {
    return {
      subject: '🎉 Benvenuto in VideoChat Couple - La tua videochat è pronta!',
      html: `
        <!DOCTYPE html>
        <html lang="it">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Benvenuto in VideoChat Couple</title>
          <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body {
              font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: #ffffff;
              line-height: 1.6;
              padding: 20px;
            }
            .container {
              max-width: 600px;
              margin: 0 auto;
              background: rgba(255, 255, 255, 0.1);
              backdrop-filter: blur(20px);
              border-radius: 20px;
              border: 1px solid rgba(255, 255, 255, 0.2);
              overflow: hidden;
              box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            }
            .header {
              background: linear-gradient(135deg, rgba(15, 15, 35, 0.9), rgba(26, 26, 46, 0.9));
              padding: 40px 30px;
              text-align: center;
              border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            }
            .logo {
              font-size: 32px;
              font-weight: 800;
              background: linear-gradient(135deg, #667eea, #764ba2);
              -webkit-background-clip: text;
              background-clip: text;
              color: transparent;
              margin-bottom: 10px;
            }
            .subtitle {
              font-size: 16px;
              color: rgba(255, 255, 255, 0.8);
            }
            .content {
              padding: 40px 30px;
            }
            .welcome-title {
              font-size: 28px;
              font-weight: 700;
              margin-bottom: 20px;
              background: linear-gradient(135deg, #00c6ff, #0072ff);
              -webkit-background-clip: text;
              background-clip: text;
              color: transparent;
            }
            .feature {
              display: flex;
              align-items: center;
              margin: 20px 0;
              padding: 15px;
              background: rgba(255, 255, 255, 0.05);
              border-radius: 12px;
              border-left: 4px solid #667eea;
            }
            .feature-icon {
              font-size: 24px;
              margin-right: 15px;
            }
            .feature-text {
              font-size: 16px;
              color: rgba(255, 255, 255, 0.9);
            }
            .cta-button {
              display: inline-block;
              background: linear-gradient(135deg, #667eea, #764ba2);
              color: white;
              text-decoration: none;
              padding: 16px 32px;
              border-radius: 12px;
              font-weight: 600;
              font-size: 16px;
              margin: 20px 0;
              transition: all 0.3s ease;
              box-shadow: 0 5px 20px rgba(103, 126, 234, 0.3);
            }
            .stats {
              background: rgba(255, 255, 255, 0.05);
              padding: 25px;
              border-radius: 15px;
              margin: 25px 0;
              text-align: center;
            }
            .stat-item {
              display: inline-block;
              margin: 0 20px;
              text-align: center;
            }
            .stat-number {
              font-size: 24px;
              font-weight: 700;
              color: #51cf66;
            }
            .stat-label {
              font-size: 12px;
              color: rgba(255, 255, 255, 0.7);
              text-transform: uppercase;
            }
            .footer {
              background: rgba(15, 15, 35, 0.8);
              padding: 30px;
              text-align: center;
              border-top: 1px solid rgba(255, 255, 255, 0.1);
            }
            .footer-text {
              font-size: 14px;
              color: rgba(255, 255, 255, 0.6);
              margin-bottom: 15px;
            }
            .social-links {
              margin: 20px 0;
            }
            .social-link {
              display: inline-block;
              margin: 0 10px;
              color: #667eea;
              text-decoration: none;
              font-weight: 500;
            }
            @media (max-width: 600px) {
              .container { margin: 10px; }
              .content { padding: 20px; }
              .stat-item { display: block; margin: 10px 0; }
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <div class="logo">VideoChat Couple</div>
              <div class="subtitle">Videochat Professionale di Nuova Generazione</div>
            </div>
            
            <div class="content">
              <h1 class="welcome-title">🎉 Benvenuto nella Community!</h1>
              
              <p style="font-size: 18px; margin-bottom: 25px; color: rgba(255, 255, 255, 0.9);">
                Ciao <strong>${userEmail}</strong>!<br>
                Il tuo account è stato creato con successo. Sei ora parte della community VideoChat Couple, 
                la piattaforma di videochat più avanzata e sicura disponibile.
              </p>

              <div class="feature">
                <div class="feature-icon">🎥</div>
                <div class="feature-text">
                  <strong>Video HD di Qualità</strong><br>
                  Videochat in alta definizione con tecnologia WebRTC avanzata
                </div>
              </div>

              <div class="feature">
                <div class="feature-icon">🔒</div>
                <div class="feature-text">
                  <strong>Privacy e Sicurezza</strong><br>
                  Nessuna registrazione, sistema di report avanzato, moderazione automatica
                </div>
              </div>

              <div class="feature">
                <div class="feature-icon">💬</div>
                <div class="feature-text">
                  <strong>Chat Integrata</strong><br>
                  Sistema di messaggistica in tempo reale durante le videochat
                </div>
              </div>

              <div class="feature">
                <div class="feature-icon">🎉</div>
                <div class="feature-text">
                  <strong>Completamente Gratuito</strong><br>
                  Servizio gratuito fino a 10.000 utenti registrati
                </div>
              </div>

              <div class="stats">
                <h3 style="margin-bottom: 20px; color: #fff;">La Community VideoChat Couple</h3>
                <div class="stat-item">
                  <div class="stat-number">10K+</div>
                  <div class="stat-label">Utenti Attivi</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">24/7</div>
                  <div class="stat-label">Disponibilità</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">HD</div>
                  <div class="stat-label">Qualità Video</div>
                </div>
              </div>

              <div style="text-align: center; margin: 30px 0;">
                <a href="https://videochatcouple.com" class="cta-button">
                  🚀 Inizia la tua Prima VideoChat
                </a>
              </div>

              <div style="background: rgba(255, 193, 7, 0.1); padding: 20px; border-radius: 12px; border-left: 4px solid #ffc107; margin: 25px 0;">
                <h4 style="color: #ffc107; margin-bottom: 10px;">💡 Suggerimenti per Iniziare</h4>
                <ul style="color: rgba(255, 255, 255, 0.8); padding-left: 20px;">
                  <li>Assicurati di avere una webcam e microfono funzionanti</li>
                  <li>Usa Chrome, Firefox o Edge per la migliore esperienza</li>
                  <li>Sii rispettoso: la community è moderata attivamente</li>
                  <li>Usa il sistema di report per segnalare comportamenti inappropriati</li>
                </ul>
              </div>
            </div>

            <div class="footer">
              <div class="footer-text">
                Grazie per aver scelto VideoChat Couple!<br>
                Per supporto o domande, rispondi a questa email.
              </div>
              
              <div class="social-links">
                <a href="https://videochatcouple.com" class="social-link">🌐 Sito Web</a>
                <a href="mailto:<EMAIL>" class="social-link">📧 Supporto</a>
              </div>
              
              <div style="font-size: 12px; color: rgba(255, 255, 255, 0.5); margin-top: 20px;">
                © 2024 VideoChat Couple. Tutti i diritti riservati.<br>
                Questa email è stata inviata a ${userEmail}
              </div>
            </div>
          </div>
        </body>
        </html>
      `,
      text: `
        Benvenuto in VideoChat Couple!
        
        Ciao ${userEmail}!
        
        Il tuo account è stato creato con successo. Sei ora parte della community VideoChat Couple.
        
        Caratteristiche principali:
        - Video HD di qualità con tecnologia WebRTC
        - Privacy e sicurezza garantite
        - Chat integrata in tempo reale
        - Servizio completamente gratuito
        
        Inizia subito: https://videochatcouple.com
        
        Per supporto: <EMAIL>
        
        Grazie per aver scelto VideoChat Couple!
      `
    };
  }

  // Invia email di benvenuto
  async sendWelcomeEmail(userEmail) {
    try {
      const emailTemplate = this.getWelcomeEmailTemplate(userEmail);
      
      const mailOptions = {
        from: {
          name: 'VideoChat Couple',
          address: '<EMAIL>'
        },
        to: userEmail,
        subject: emailTemplate.subject,
        html: emailTemplate.html,
        text: emailTemplate.text
      };

      const result = await this.transporter.sendMail(mailOptions);
      logger.info(`✅ Email di benvenuto inviata a: ${userEmail}`);
      return { success: true, messageId: result.messageId };
      
    } catch (error) {
      logger.error(`❌ Errore invio email a ${userEmail}:`, error);
      return { success: false, error: error.message };
    }
  }

  // Invia email di supporto
  async sendSupportEmail(userEmail, subject, message) {
    try {
      const mailOptions = {
        from: {
          name: 'VideoChat Couple Support',
          address: '<EMAIL>'
        },
        to: userEmail,
        subject: `[VideoChat Couple] ${subject}`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #667eea;">VideoChat Couple - Supporto</h2>
            <p>${message}</p>
            <hr>
            <p style="font-size: 12px; color: #666;">
              Questo messaggio è stato inviato dal team di supporto VideoChat Couple.
            </p>
          </div>
        `,
        text: message
      };

      const result = await this.transporter.sendMail(mailOptions);
      logger.info(`✅ Email di supporto inviata a: ${userEmail}`);
      return { success: true, messageId: result.messageId };
      
    } catch (error) {
      logger.error(`❌ Errore invio email supporto a ${userEmail}:`, error);
      return { success: false, error: error.message };
    }
  }
}

module.exports = new EmailService();
