/**
 * Servizio per la gestione dei pagamenti
 */
import config from '../config/config.js';
import logger from '../config/logging/logger.js';
import Transaction from '../models/transaction.model.js';
import User from '../models/user.model.js';
import * as notificationService from './notification.service.js';

// Logger specifico per il modulo pagamenti
const paymentLogger = logger.getModuleLogger('payment');

/**
 * Crea una transazione di acquisto
 * @param {Object} options - Opzioni per la transazione
 * @returns {Promise<Transaction>} Promise che si risolve con la transazione creata
 */
const createPurchaseTransaction = async (options) => {
  try {
    // Verifica che l'utente esista
    const user = await User.findById(options.userId);
    if (!user) {
      throw new Error('Utente non trovato');
    }

    // Crea la transazione
    const transaction = await Transaction.createPurchase({
      userId: options.userId,
      amount: options.amount,
      credits: options.credits,
      currency: options.currency || 'EUR',
      paymentMethod: options.paymentMethod,
      description: options.description || `Acquisto di ${options.credits} crediti`,
      metadata: options.metadata,
      ipAddress: options.ipAddress,
      userAgent: options.userAgent
    });

    paymentLogger.info(`Transazione di acquisto creata: ${transaction._id}`);

    return transaction;
  } catch (error) {
    paymentLogger.error('Errore creazione transazione di acquisto:', error);
    throw error;
  }
};

/**
 * Completa una transazione di acquisto
 * @param {string} transactionId - ID della transazione
 * @param {Object} options - Opzioni per il completamento
 * @returns {Promise<Transaction>} Promise che si risolve con la transazione completata
 */
const completePurchaseTransaction = async (transactionId, options = {}) => {
  try {
    // Trova la transazione
    const transaction = await Transaction.findById(transactionId);
    if (!transaction) {
      throw new Error('Transazione non trovata');
    }

    // Verifica che la transazione sia in stato pending
    if (transaction.status !== 'pending') {
      throw new Error(`Transazione in stato non valido: ${transaction.status}`);
    }

    // Completa la transazione
    await transaction.complete({
      paymentId: options.paymentId,
      metadata: options.metadata
    });

    paymentLogger.info(`Transazione di acquisto completata: ${transaction._id}`);

    // Invia notifica all'utente
    await notificationService.sendCreditPurchaseNotification(
      transaction.userId,
      transaction.credits,
      transaction.amount,
      transaction.currency
    );

    return transaction;
  } catch (error) {
    paymentLogger.error('Errore completamento transazione di acquisto:', error);
    throw error;
  }
};

/**
 * Fallisce una transazione di acquisto
 * @param {string} transactionId - ID della transazione
 * @param {Object} options - Opzioni per il fallimento
 * @returns {Promise<Transaction>} Promise che si risolve con la transazione fallita
 */
const failPurchaseTransaction = async (transactionId, options = {}) => {
  try {
    // Trova la transazione
    const transaction = await Transaction.findById(transactionId);
    if (!transaction) {
      throw new Error('Transazione non trovata');
    }

    // Verifica che la transazione sia in stato pending
    if (transaction.status !== 'pending') {
      throw new Error(`Transazione in stato non valido: ${transaction.status}`);
    }

    // Fallisci la transazione
    await transaction.fail({
      description: options.description || 'Pagamento fallito',
      metadata: options.metadata
    });

    paymentLogger.info(`Transazione di acquisto fallita: ${transaction._id}`);

    return transaction;
  } catch (error) {
    paymentLogger.error('Errore fallimento transazione di acquisto:', error);
    throw error;
  }
};

/**
 * Rimborsa una transazione di acquisto
 * @param {string} transactionId - ID della transazione
 * @param {Object} options - Opzioni per il rimborso
 * @returns {Promise<Transaction>} Promise che si risolve con la transazione rimborsata
 */
const refundPurchaseTransaction = async (transactionId, options = {}) => {
  try {
    // Trova la transazione
    const transaction = await Transaction.findById(transactionId);
    if (!transaction) {
      throw new Error('Transazione non trovata');
    }

    // Verifica che la transazione sia in stato completed
    if (transaction.status !== 'completed') {
      throw new Error(`Transazione in stato non valido: ${transaction.status}`);
    }

    // Rimborsa la transazione
    await transaction.refund({
      description: options.description || 'Rimborso richiesto',
      metadata: options.metadata,
      ipAddress: options.ipAddress,
      userAgent: options.userAgent
    });

    paymentLogger.info(`Transazione di acquisto rimborsata: ${transaction._id}`);

    return transaction;
  } catch (error) {
    paymentLogger.error('Errore rimborso transazione di acquisto:', error);
    throw error;
  }
};

/**
 * Crea una transazione di utilizzo
 * @param {Object} options - Opzioni per la transazione
 * @returns {Promise<Transaction>} Promise che si risolve con la transazione creata
 */
const createUsageTransaction = async (options) => {
  try {
    // Verifica che l'utente esista
    const user = await User.findById(options.userId);
    if (!user) {
      throw new Error('Utente non trovato');
    }

    // Verifica che l'utente abbia crediti sufficienti
    if (user.credits < options.credits) {
      throw new Error('Crediti insufficienti');
    }

    // Crea la transazione
    const transaction = await Transaction.createUsage({
      userId: options.userId,
      credits: options.credits,
      description: options.description || `Utilizzo di ${options.credits} crediti`,
      sessionId: options.sessionId,
      ipAddress: options.ipAddress,
      userAgent: options.userAgent
    });

    paymentLogger.info(`Transazione di utilizzo creata: ${transaction._id}`);

    // Verifica se i crediti sono in esaurimento
    if (user.credits < 5) {
      await notificationService.sendLowCreditsNotification(user._id, user.credits);
    }

    return transaction;
  } catch (error) {
    paymentLogger.error('Errore creazione transazione di utilizzo:', error);
    throw error;
  }
};

/**
 * Ottiene il bilancio delle transazioni di un utente
 * @param {string} userId - ID dell'utente
 * @returns {Promise<Object>} Promise che si risolve con il bilancio
 */
const getUserBalance = async (userId) => {
  try {
    // Verifica che l'utente esista
    const user = await User.findById(userId);
    if (!user) {
      throw new Error('Utente non trovato');
    }

    // Ottieni il bilancio
    const balance = await Transaction.getUserBalance(userId);

    return {
      ...balance,
      currentCredits: user.credits
    };
  } catch (error) {
    paymentLogger.error('Errore recupero bilancio utente:', error);
    throw error;
  }
};

/**
 * Ottiene le transazioni di un utente
 * @param {string} userId - ID dell'utente
 * @param {Object} options - Opzioni di ricerca
 * @returns {Promise<Array>} Promise che si risolve con le transazioni trovate
 */
const getUserTransactions = async (userId, options = {}) => {
  try {
    // Verifica che l'utente esista
    const user = await User.findById(userId);
    if (!user) {
      throw new Error('Utente non trovato');
    }

    // Costruisci la query
    const query = { userId };

    if (options.type) {
      query.type = options.type;
    }

    if (options.status) {
      query.status = options.status;
    }

    // Esegui la query
    const transactions = await Transaction.find(query)
      .sort({ createdAt: -1 })
      .limit(options.limit || 100)
      .skip(options.skip || 0);

    return transactions;
  } catch (error) {
    paymentLogger.error('Errore recupero transazioni utente:', error);
    throw error;
  }
};

/**
 * Aggiunge crediti bonus a un utente
 * @param {string} userId - ID dell'utente
 * @param {number} credits - Numero di crediti da aggiungere
 * @param {string} reason - Motivo dell'aggiunta
 * @returns {Promise<Transaction>} Promise che si risolve con la transazione creata
 */
const addBonusCredits = async (userId, credits, reason = 'Crediti bonus') => {
  try {
    // Verifica che l'utente esista
    const user = await User.findById(userId);
    if (!user) {
      throw new Error('Utente non trovato');
    }

    // Crea la transazione
    const transaction = new Transaction({
      userId,
      type: 'bonus',
      amount: 0,
      credits,
      status: 'completed',
      paymentMethod: 'system',
      description: reason
    });

    // Aggiorna i crediti dell'utente
    user.credits += credits;
    await user.save();

    // Salva la transazione
    await transaction.save();

    paymentLogger.info(`Crediti bonus aggiunti: ${credits} a ${userId}`);

    return transaction;
  } catch (error) {
    paymentLogger.error('Errore aggiunta crediti bonus:', error);
    throw error;
  }
};

export {
  createPurchaseTransaction,
  completePurchaseTransaction,
  failPurchaseTransaction,
  refundPurchaseTransaction,
  createUsageTransaction,
  getUserBalance,
  getUserTransactions,
  addBonusCredits
};
