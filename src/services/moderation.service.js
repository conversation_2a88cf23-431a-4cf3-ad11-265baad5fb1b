/**
 * Servizio per la moderazione dei contenuti
 */
import config from '../config/config.js';
import logger from '../config/logger.js';
import User from '../models/user.model.js';
import Report from '../models/report.model.js';
import Session from '../models/session.model.js';
import * as emailService from './email.service.js';

// Logger specifico per il modulo moderazione
const moderationLogger = logger.getModuleLogger('moderation');

/**
 * Crea un report
 * @param {Object} options - Opzioni per il report
 * @returns {Promise<Report>} Promise che si risolve con il report creato
 */
const createReport = async (options) => {
  try {
    // Verifica che l'utente che fa il report esista
    const reporter = await User.findById(options.reporterId);
    if (!reporter) {
      throw new Error('Utente che fa il report non trovato');
    }

    // Verifica che l'utente segnalato esista
    const reported = await User.findById(options.reportedId);
    if (!reported) {
      throw new Error('Utente segnalato non trovato');
    }

    // Verifica che la sessione esista
    const session = await Session.findById(options.sessionId);
    if (!session) {
      throw new Error('Sessione non trovata');
    }

    // Crea il report
    const report = new Report({
      reporterId: options.reporterId,
      reportedId: options.reportedId,
      sessionId: options.sessionId,
      reason: options.reason,
      description: options.description,
      evidence: options.evidence || [],
      ipAddress: options.ipAddress,
      userAgent: options.userAgent
    });

    await report.save();

    moderationLogger.info(`Report creato: ${report._id}`);

    // Aggiorna la sessione
    await session.reportUser(
      session.userId.toString() === options.reporterId.toString() ? 'user' : 'partner',
      options.reason
    );

    // Verifica se l'utente ha ricevuto molti report
    const reportCount = await Report.countByReportedUser(options.reportedId);

    // Se l'utente ha ricevuto molti report, prendi provvedimenti
    if (reportCount.total >= 5) {
      await takeActionAgainstUser(options.reportedId, 'warning');
    }

    if (reportCount.total >= 10) {
      await takeActionAgainstUser(options.reportedId, 'temporary_ban');
    }

    if (reportCount.total >= 20) {
      await takeActionAgainstUser(options.reportedId, 'permanent_ban');
    }

    return report;
  } catch (error) {
    moderationLogger.error('Errore creazione report:', error);
    throw error;
  }
};

/**
 * Prende provvedimenti contro un utente
 * @param {string} userId - ID dell'utente
 * @param {string} action - Azione da intraprendere
 * @returns {Promise<User>} Promise che si risolve con l'utente aggiornato
 */
const takeActionAgainstUser = async (userId, action) => {
  try {
    // Trova l'utente
    const user = await User.findById(userId);
    if (!user) {
      throw new Error('Utente non trovato');
    }

    // Prendi provvedimenti in base all'azione
    switch (action) {
      case 'warning':
        // Invia un'email di avvertimento
        await sendWarningEmail(user);
        moderationLogger.info(`Avvertimento inviato a ${user.email}`);
        break;

      case 'temporary_ban':
        // Sospendi l'utente per 7 giorni
        user.status = 'suspended';
        await user.save();

        // Invia un'email di sospensione
        await sendSuspensionEmail(user, 7);
        moderationLogger.info(`Utente ${user.email} sospeso temporaneamente`);

        // Pianifica la riattivazione dell'utente
        setTimeout(async () => {
          try {
            const suspendedUser = await User.findById(userId);
            if (suspendedUser && suspendedUser.status === 'suspended') {
              suspendedUser.status = 'active';
              await suspendedUser.save();
              moderationLogger.info(`Utente ${suspendedUser.email} riattivato`);
            }
          } catch (error) {
            moderationLogger.error('Errore riattivazione utente:', error);
          }
        }, 7 * 24 * 60 * 60 * 1000); // 7 giorni
        break;

      case 'permanent_ban':
        // Banna l'utente permanentemente
        user.status = 'banned';
        await user.save();

        // Invia un'email di ban
        await sendBanEmail(user);
        moderationLogger.info(`Utente ${user.email} bannato permanentemente`);
        break;

      default:
        throw new Error(`Azione non valida: ${action}`);
    }

    return user;
  } catch (error) {
    moderationLogger.error('Errore azione contro utente:', error);
    throw error;
  }
};

/**
 * Invia un'email di avvertimento
 * @param {Object} user - Utente
 * @returns {Promise<void>}
 */
const sendWarningEmail = async (user) => {
  try {
    const subject = `Avvertimento - ${config.app.name}`;

    const text = `
      Ciao,

      Abbiamo ricevuto diverse segnalazioni riguardanti il tuo comportamento su ${config.app.name}.

      Ti ricordiamo che è importante rispettare le nostre linee guida della community:

      1. Tratta gli altri utenti con rispetto
      2. Non condividere contenuti inappropriati o offensivi
      3. Non molestare o minacciare altri utenti
      4. Non utilizzare la piattaforma per attività illegali

      Questo è un avvertimento. Se continuerai a violare le nostre linee guida, potremmo sospendere o bannare il tuo account.

      Se ritieni che questo avvertimento sia stato inviato per errore, contattaci rispondendo a questa email.

      Cordiali saluti,
      Il team di moderazione di ${config.app.name}
    `;

    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
        <h2 style="color: #e74c3c;">Avvertimento</h2>
        <p>Ciao,</p>
        <p>Abbiamo ricevuto diverse segnalazioni riguardanti il tuo comportamento su ${config.app.name}.</p>
        <p>Ti ricordiamo che è importante rispettare le nostre linee guida della community:</p>
        <ol>
          <li>Tratta gli altri utenti con rispetto</li>
          <li>Non condividere contenuti inappropriati o offensivi</li>
          <li>Non molestare o minacciare altri utenti</li>
          <li>Non utilizzare la piattaforma per attività illegali</li>
        </ol>
        <p><strong>Questo è un avvertimento.</strong> Se continuerai a violare le nostre linee guida, potremmo sospendere o bannare il tuo account.</p>
        <p>Se ritieni che questo avvertimento sia stato inviato per errore, contattaci rispondendo a questa email.</p>
        <p>Cordiali saluti,<br>Il team di moderazione di ${config.app.name}</p>
      </div>
    `;

    await emailService.sendEmail({
      to: user.email,
      subject,
      text,
      html
    });
  } catch (error) {
    moderationLogger.error('Errore invio email di avvertimento:', error);
    throw error;
  }
};

/**
 * Invia un'email di sospensione
 * @param {Object} user - Utente
 * @param {number} days - Numero di giorni di sospensione
 * @returns {Promise<void>}
 */
const sendSuspensionEmail = async (user, days) => {
  try {
    const subject = `Account sospeso - ${config.app.name}`;

    const text = `
      Ciao,

      Il tuo account su ${config.app.name} è stato sospeso per ${days} giorni a causa di ripetute violazioni delle nostre linee guida della community.

      Durante questo periodo, non potrai accedere alla piattaforma o utilizzare i nostri servizi.

      Il tuo account sarà automaticamente riattivato dopo ${days} giorni.

      Se ritieni che questa sospensione sia stata applicata per errore, contattaci rispondendo a questa email.

      Cordiali saluti,
      Il team di moderazione di ${config.app.name}
    `;

    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
        <h2 style="color: #e74c3c;">Account sospeso</h2>
        <p>Ciao,</p>
        <p>Il tuo account su ${config.app.name} è stato sospeso per <strong>${days} giorni</strong> a causa di ripetute violazioni delle nostre linee guida della community.</p>
        <p>Durante questo periodo, non potrai accedere alla piattaforma o utilizzare i nostri servizi.</p>
        <p>Il tuo account sarà automaticamente riattivato dopo ${days} giorni.</p>
        <p>Se ritieni che questa sospensione sia stata applicata per errore, contattaci rispondendo a questa email.</p>
        <p>Cordiali saluti,<br>Il team di moderazione di ${config.app.name}</p>
      </div>
    `;

    await emailService.sendEmail({
      to: user.email,
      subject,
      text,
      html
    });
  } catch (error) {
    moderationLogger.error('Errore invio email di sospensione:', error);
    throw error;
  }
};

/**
 * Invia un'email di ban
 * @param {Object} user - Utente
 * @returns {Promise<void>}
 */
const sendBanEmail = async (user) => {
  try {
    const subject = `Account bannato - ${config.app.name}`;

    const text = `
      Ciao,

      Il tuo account su ${config.app.name} è stato bannato permanentemente a causa di gravi o ripetute violazioni delle nostre linee guida della community.

      Non potrai più accedere alla piattaforma o utilizzare i nostri servizi.

      Se ritieni che questo ban sia stato applicato per errore, contattaci rispondendo a questa email.

      Cordiali saluti,
      Il team di moderazione di ${config.app.name}
    `;

    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
        <h2 style="color: #e74c3c;">Account bannato</h2>
        <p>Ciao,</p>
        <p>Il tuo account su ${config.app.name} è stato <strong>bannato permanentemente</strong> a causa di gravi o ripetute violazioni delle nostre linee guida della community.</p>
        <p>Non potrai più accedere alla piattaforma o utilizzare i nostri servizi.</p>
        <p>Se ritieni che questo ban sia stato applicato per errore, contattaci rispondendo a questa email.</p>
        <p>Cordiali saluti,<br>Il team di moderazione di ${config.app.name}</p>
      </div>
    `;

    await emailService.sendEmail({
      to: user.email,
      subject,
      text,
      html
    });
  } catch (error) {
    moderationLogger.error('Errore invio email di ban:', error);
    throw error;
  }
};

/**
 * Verifica se un utente è stato segnalato recentemente
 * @param {string} userId - ID dell'utente
 * @param {number} days - Numero di giorni da considerare
 * @returns {Promise<boolean>} Promise che si risolve con true se l'utente è stato segnalato
 */
const hasRecentReports = async (userId, days = 7) => {
  try {
    return await Report.hasRecentReports(userId, days);
  } catch (error) {
    moderationLogger.error('Errore verifica report recenti:', error);
    throw error;
  }
};

/**
 * Ottiene i report pendenti
 * @param {Object} options - Opzioni di ricerca
 * @returns {Promise<Array>} Promise che si risolve con i report trovati
 */
const getPendingReports = async (options = {}) => {
  try {
    return await Report.findPending(options);
  } catch (error) {
    moderationLogger.error('Errore recupero report pendenti:', error);
    throw error;
  }
};

/**
 * Aggiorna lo stato di un report
 * @param {string} reportId - ID del report
 * @param {string} status - Nuovo stato
 * @param {Object} options - Opzioni aggiuntive
 * @returns {Promise<Report>} Promise che si risolve con il report aggiornato
 */
const updateReportStatus = async (reportId, status, options = {}) => {
  try {
    // Trova il report
    const report = await Report.findById(reportId);
    if (!report) {
      throw new Error('Report non trovato');
    }

    // Aggiorna lo stato
    await report.updateStatus(status, options);

    moderationLogger.info(`Report ${reportId} aggiornato a ${status}`);

    // Se è stato preso un provvedimento, aggiorna l'utente
    if (options.actionTaken && options.actionTaken !== 'none') {
      await takeActionAgainstUser(report.reportedId, options.actionTaken);
    }

    return report;
  } catch (error) {
    moderationLogger.error('Errore aggiornamento stato report:', error);
    throw error;
  }
};

export {
  createReport,
  takeActionAgainstUser,
  hasRecentReports,
  getPendingReports,
  updateReportStatus
};
