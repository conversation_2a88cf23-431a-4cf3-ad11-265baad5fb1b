/**
 * <PERSON><PERSON><PERSON> per l'invio di email
 */
import nodemailer from 'nodemailer';
import config from '../config/config.js';
import logger from '../config/logger.js';

// Logger specifico per il modulo email
const emailLogger = logger.getModuleLogger('email');

// Crea il trasportatore per l'invio delle email
const transporter = nodemailer.createTransport({
  host: config.email.host,
  port: config.email.port,
  secure: config.email.secure,
  auth: {
    user: config.email.auth.user,
    pass: config.email.auth.pass
  }
});

/**
 * Invia un'email
 * @param {Object} options - Opzioni per l'email
 * @param {string} options.to - Destinatario
 * @param {string} options.subject - Oggetto
 * @param {string} options.text - Testo dell'email
 * @param {string} options.html - HTML dell'email
 * @returns {Promise} Promise che si risolve con le informazioni sull'invio
 */
const sendEmail = async (options) => {
  try {
    const mailOptions = {
      from: config.email.from,
      to: options.to,
      subject: options.subject,
      text: options.text,
      html: options.html
    };

    const info = await transporter.sendMail(mailOptions);
    emailLogger.info(`Email inviata: ${info.messageId}`);
    return info;
  } catch (error) {
    emailLogger.error('Errore invio email:', error);
    throw error;
  }
};

/**
 * Invia un'email di verifica
 * @param {string} to - Email del destinatario
 * @param {string} token - Token di verifica
 * @returns {Promise} Promise che si risolve con le informazioni sull'invio
 */
const sendVerificationEmail = async (to, token) => {
  const verificationUrl = `${config.server.baseUrl}/auth/verify/${token}`;

  const subject = `Verifica il tuo account ${config.app.name}`;

  const text = `
    Grazie per esserti registrato su ${config.app.name}!

    Per completare la registrazione, verifica il tuo account cliccando sul seguente link:
    ${verificationUrl}

    Il link scadrà tra 24 ore.

    Se non hai richiesto questa email, puoi ignorarla.

    Cordiali saluti,
    Il team di ${config.app.name}
  `;

  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
      <h2 style="color: #3498db;">Verifica il tuo account ${config.app.name}</h2>
      <p>Grazie per esserti registrato su ${config.app.name}!</p>
      <p>Per completare la registrazione, verifica il tuo account cliccando sul seguente link:</p>
      <p style="text-align: center;">
        <a href="${verificationUrl}" style="display: inline-block; padding: 10px 20px; background-color: #3498db; color: white; text-decoration: none; border-radius: 5px;">Verifica Account</a>
      </p>
      <p>Oppure copia e incolla questo link nel tuo browser:</p>
      <p style="word-break: break-all; background-color: #f8f9fa; padding: 10px; border-radius: 5px;">${verificationUrl}</p>
      <p>Il link scadrà tra 24 ore.</p>
      <p>Se non hai richiesto questa email, puoi ignorarla.</p>
      <p>Cordiali saluti,<br>Il team di ${config.app.name}</p>
    </div>
  `;

  return sendEmail({ to, subject, text, html });
};

/**
 * Invia un'email di reset password
 * @param {string} to - Email del destinatario
 * @param {string} token - Token di reset
 * @returns {Promise} Promise che si risolve con le informazioni sull'invio
 */
const sendPasswordResetEmail = async (to, token) => {
  const resetUrl = `${config.server.baseUrl}/reset-password/${token}`;

  const subject = `Reset Password - ${config.app.name}`;

  const text = `
    Hai richiesto il reset della password su ${config.app.name}.

    Per reimpostare la tua password, clicca sul seguente link:
    ${resetUrl}

    Il link scadrà tra 1 ora.

    Se non hai richiesto il reset della password, puoi ignorare questa email.

    Cordiali saluti,
    Il team di ${config.app.name}
  `;

  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
      <h2 style="color: #3498db;">Reset Password - ${config.app.name}</h2>
      <p>Hai richiesto il reset della password su ${config.app.name}.</p>
      <p>Per reimpostare la tua password, clicca sul seguente link:</p>
      <p style="text-align: center;">
        <a href="${resetUrl}" style="display: inline-block; padding: 10px 20px; background-color: #3498db; color: white; text-decoration: none; border-radius: 5px;">Reset Password</a>
      </p>
      <p>Oppure copia e incolla questo link nel tuo browser:</p>
      <p style="word-break: break-all; background-color: #f8f9fa; padding: 10px; border-radius: 5px;">${resetUrl}</p>
      <p>Il link scadrà tra 1 ora.</p>
      <p>Se non hai richiesto il reset della password, puoi ignorare questa email.</p>
      <p>Cordiali saluti,<br>Il team di ${config.app.name}</p>
    </div>
  `;

  return sendEmail({ to, subject, text, html });
};

/**
 * Invia un'email di benvenuto
 * @param {string} to - Email del destinatario
 * @param {string} name - Nome del destinatario
 * @returns {Promise} Promise che si risolve con le informazioni sull'invio
 */
const sendWelcomeEmail = async (to, name = 'Utente') => {
  const subject = `Benvenuto su ${config.app.name}!`;

  const text = `
    Ciao ${name},

    Benvenuto su ${config.app.name}!

    Siamo felici di averti con noi. Ecco alcune informazioni utili:

    - Hai ricevuto ${config.app.initialCredits} crediti gratuiti per iniziare
    - Puoi utilizzare la piattaforma per connetterti con persone da tutto il mondo
    - La tua privacy e sicurezza sono la nostra priorità

    Se hai domande, non esitare a contattarci.

    Cordiali saluti,
    Il team di ${config.app.name}
  `;

  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
      <h2 style="color: #3498db;">Benvenuto su ${config.app.name}!</h2>
      <p>Ciao ${name},</p>
      <p>Siamo felici di averti con noi. Ecco alcune informazioni utili:</p>
      <ul>
        <li>Hai ricevuto <strong>${config.app.initialCredits} crediti gratuiti</strong> per iniziare</li>
        <li>Puoi utilizzare la piattaforma per connetterti con persone da tutto il mondo</li>
        <li>La tua privacy e sicurezza sono la nostra priorità</li>
      </ul>
      <p>Se hai domande, non esitare a contattarci.</p>
      <p>Cordiali saluti,<br>Il team di ${config.app.name}</p>
    </div>
  `;

  return sendEmail({ to, subject, text, html });
};

export {
  sendEmail,
  sendVerificationEmail,
  sendPasswordResetEmail,
  sendWelcomeEmail
};
