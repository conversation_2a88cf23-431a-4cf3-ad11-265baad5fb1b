const express = require('express');
const router = express.Router();
const Report = require('../models/Report');
const User = require('../models/user.model');
const { authenticateToken } = require('../middleware/auth');
const logger = require('../utils/logger');

// Crea una nuova segnalazione
router.post('/create', authenticateToken, async (req, res) => {
  try {
    const { reportedUserId, reason, description, chatId } = req.body;
    const reporterId = req.user.id;

    // Validazione input
    if (!reportedUserId || !reason) {
      return res.status(400).json({
        success: false,
        message: 'ID utente segnalato e motivo sono obbligatori'
      });
    }

    // Verifica che l'utente segnalato esista
    const reportedUser = await User.findById(reportedUserId);
    if (!reportedUser) {
      return res.status(404).json({
        success: false,
        message: 'Utente segnalato non trovato'
      });
    }

    // Verifica che non si stia auto-segnalando
    if (reporterId === reportedUserId) {
      return res.status(400).json({
        success: false,
        message: 'Non puoi segnalare te stesso'
      });
    }

    // Crea la segnalazione
    const report = new Report({
      reporterId,
      reportedUserId,
      reason,
      description: description || '',
      chatId: chatId || null,
      status: 'pending',
      createdAt: new Date()
    });

    await report.save();

    // Incrementa il contatore di segnalazioni dell'utente
    await User.findByIdAndUpdate(reportedUserId, {
      $inc: { reportCount: 1 }
    });

    // Log della segnalazione
    logger.info(`Report creato: ${reporterId} ha segnalato ${reportedUserId} per ${reason}`);

    // Se l'utente ha troppe segnalazioni, sospendilo automaticamente
    const updatedUser = await User.findById(reportedUserId);
    if (updatedUser.reportCount >= 5) {
      await User.findByIdAndUpdate(reportedUserId, {
        status: 'suspended',
        suspendedAt: new Date(),
        suspendedReason: 'Troppe segnalazioni ricevute'
      });
      
      logger.warn(`Utente ${reportedUserId} sospeso automaticamente per troppe segnalazioni`);
    }

    res.json({
      success: true,
      message: 'Segnalazione inviata con successo',
      reportId: report._id
    });

  } catch (error) {
    logger.error('Errore creazione report:', error);
    res.status(500).json({
      success: false,
      message: 'Errore interno del server'
    });
  }
});

// Blocca un utente
router.post('/block', authenticateToken, async (req, res) => {
  try {
    const { blockedUserId } = req.body;
    const blockerId = req.user.id;

    if (!blockedUserId) {
      return res.status(400).json({
        success: false,
        message: 'ID utente da bloccare è obbligatorio'
      });
    }

    // Verifica che l'utente da bloccare esista
    const blockedUser = await User.findById(blockedUserId);
    if (!blockedUser) {
      return res.status(404).json({
        success: false,
        message: 'Utente da bloccare non trovato'
      });
    }

    // Verifica che non si stia auto-bloccando
    if (blockerId === blockedUserId) {
      return res.status(400).json({
        success: false,
        message: 'Non puoi bloccare te stesso'
      });
    }

    // Aggiungi alla lista dei bloccati
    await User.findByIdAndUpdate(blockerId, {
      $addToSet: { blockedUsers: blockedUserId }
    });

    logger.info(`Utente ${blockerId} ha bloccato ${blockedUserId}`);

    res.json({
      success: true,
      message: 'Utente bloccato con successo'
    });

  } catch (error) {
    logger.error('Errore blocco utente:', error);
    res.status(500).json({
      success: false,
      message: 'Errore interno del server'
    });
  }
});

// Sblocca un utente
router.post('/unblock', authenticateToken, async (req, res) => {
  try {
    const { unblockedUserId } = req.body;
    const unblockerId = req.user.id;

    if (!unblockedUserId) {
      return res.status(400).json({
        success: false,
        message: 'ID utente da sbloccare è obbligatorio'
      });
    }

    // Rimuovi dalla lista dei bloccati
    await User.findByIdAndUpdate(unblockerId, {
      $pull: { blockedUsers: unblockedUserId }
    });

    logger.info(`Utente ${unblockerId} ha sbloccato ${unblockedUserId}`);

    res.json({
      success: true,
      message: 'Utente sbloccato con successo'
    });

  } catch (error) {
    logger.error('Errore sblocco utente:', error);
    res.status(500).json({
      success: false,
      message: 'Errore interno del server'
    });
  }
});

// Ottieni lista utenti bloccati
router.get('/blocked', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    
    const user = await User.findById(userId)
      .populate('blockedUsers', 'email createdAt')
      .select('blockedUsers');

    res.json({
      success: true,
      blockedUsers: user.blockedUsers || []
    });

  } catch (error) {
    logger.error('Errore recupero utenti bloccati:', error);
    res.status(500).json({
      success: false,
      message: 'Errore interno del server'
    });
  }
});

// Ottieni statistiche report (solo per admin)
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    // Verifica se l'utente è admin (implementare logica admin)
    const user = await User.findById(req.user.id);
    if (!user.isAdmin) {
      return res.status(403).json({
        success: false,
        message: 'Accesso negato'
      });
    }

    const totalReports = await Report.countDocuments();
    const pendingReports = await Report.countDocuments({ status: 'pending' });
    const resolvedReports = await Report.countDocuments({ status: 'resolved' });
    const suspendedUsers = await User.countDocuments({ status: 'suspended' });

    res.json({
      success: true,
      stats: {
        totalReports,
        pendingReports,
        resolvedReports,
        suspendedUsers
      }
    });

  } catch (error) {
    logger.error('Errore statistiche report:', error);
    res.status(500).json({
      success: false,
      message: 'Errore interno del server'
    });
  }
});

module.exports = router;
