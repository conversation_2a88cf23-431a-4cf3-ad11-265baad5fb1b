const express = require('express');
// const rateLimit = require('express-rate-limit'); // Temporaneamente disabilitato
const { authenticateToken } = require('../middleware/auth.middleware');
const emailService = require('../services/emailService');
const logger = require('../utils/logger');

const router = express.Router();

// Rate limiting temporaneamente disabilitato per risolvere conflitti trust proxy
/*
const supportLimiter = rateLimit({
    windowMs: 60 * 60 * 1000, // 1 ora
    max: 3, // Massimo 3 richieste di supporto per ora
    message: {
        success: false,
        message: 'Troppe richieste di supporto. Riprova tra un\'ora.'
    },
    standardHeaders: true,
    legacyHeaders: false,
    // Configurazione sicura per evitare errori trust proxy
    keyGenerator: (req) => {
        return req.ip || req.connection.remoteAddress || 'unknown';
    },
    skip: (req) => {
        // Skip rate limiting per localhost in sviluppo
        return req.ip === '127.0.0.1' || req.ip === '::1';
    }
});
*/

// Invia richiesta di supporto (rate limiting temporaneamente disabilitato)
router.post('/contact', authenticateToken, async (req, res) => {
    try {
        const { subject, message, category } = req.body;
        const userEmail = req.user.email;

        // Validazione input
        if (!subject || !message) {
            return res.status(400).json({
                success: false,
                message: 'Oggetto e messaggio sono obbligatori'
            });
        }

        if (subject.length > 100) {
            return res.status(400).json({
                success: false,
                message: 'L\'oggetto non può superare i 100 caratteri'
            });
        }

        if (message.length > 1000) {
            return res.status(400).json({
                success: false,
                message: 'Il messaggio non può superare i 1000 caratteri'
            });
        }

        // Categorie supporto
        const validCategories = [
            'technical', 'account', 'report', 'suggestion', 'other'
        ];

        const categoryLabels = {
            technical: 'Problema Tecnico',
            account: 'Problema Account',
            report: 'Segnalazione',
            suggestion: 'Suggerimento',
            other: 'Altro'
        };

        const selectedCategory = validCategories.includes(category) ? category : 'other';
        const categoryLabel = categoryLabels[selectedCategory];

        // Prepara email per il team di supporto
        const supportEmailContent = `
            <div style="font-family: 'Inter', sans-serif; max-width: 600px; margin: 0 auto; background: #f8f9fa; padding: 20px;">
                <div style="background: linear-gradient(135deg, #667eea, #764ba2); padding: 20px; border-radius: 10px; color: white; margin-bottom: 20px;">
                    <h2 style="margin: 0;">🎧 Nuova Richiesta di Supporto</h2>
                    <p style="margin: 5px 0 0 0; opacity: 0.9;">VideoChat Couple</p>
                </div>
                
                <div style="background: white; padding: 25px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                    <div style="margin-bottom: 20px;">
                        <strong style="color: #667eea;">👤 Utente:</strong> ${userEmail}
                    </div>
                    
                    <div style="margin-bottom: 20px;">
                        <strong style="color: #667eea;">📂 Categoria:</strong> ${categoryLabel}
                    </div>
                    
                    <div style="margin-bottom: 20px;">
                        <strong style="color: #667eea;">📋 Oggetto:</strong> ${subject}
                    </div>
                    
                    <div style="margin-bottom: 20px;">
                        <strong style="color: #667eea;">💬 Messaggio:</strong>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 10px; border-left: 4px solid #667eea;">
                            ${message.replace(/\n/g, '<br>')}
                        </div>
                    </div>
                    
                    <div style="margin-top: 25px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666;">
                        <strong>📅 Data:</strong> ${new Date().toLocaleString('it-IT')}<br>
                        <strong>🌐 IP:</strong> ${req.ip || 'Non disponibile'}<br>
                        <strong>🔧 User Agent:</strong> ${req.get('User-Agent') || 'Non disponibile'}
                    </div>
                </div>
                
                <div style="text-align: center; margin-top: 20px; font-size: 12px; color: #666;">
                    Rispondi a questa email per contattare direttamente l'utente.
                </div>
            </div>
        `;

        // Invia email al team di supporto
        const supportResult = await emailService.transporter.sendMail({
            from: {
                name: 'VideoChat Couple Support System',
                address: '<EMAIL>'
            },
            to: '<EMAIL>',
            replyTo: userEmail,
            subject: `[SUPPORTO] ${categoryLabel}: ${subject}`,
            html: supportEmailContent,
            text: `
                Nuova richiesta di supporto VideoChat Couple
                
                Utente: ${userEmail}
                Categoria: ${categoryLabel}
                Oggetto: ${subject}
                
                Messaggio:
                ${message}
                
                Data: ${new Date().toLocaleString('it-IT')}
            `
        });

        // Invia email di conferma all'utente
        const confirmationResult = await emailService.sendSupportEmail(
            userEmail,
            'Richiesta di supporto ricevuta',
            `
                <div style="font-family: 'Inter', sans-serif;">
                    <h3 style="color: #667eea;">Grazie per aver contattato il supporto!</h3>
                    
                    <p>Abbiamo ricevuto la tua richiesta di supporto:</p>
                    
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <strong>Oggetto:</strong> ${subject}<br>
                        <strong>Categoria:</strong> ${categoryLabel}<br>
                        <strong>Data:</strong> ${new Date().toLocaleString('it-IT')}
                    </div>
                    
                    <p>Il nostro team risponderà il prima possibile, solitamente entro 24 ore.</p>
                    
                    <p style="margin-top: 20px;">
                        <strong>Team VideoChat Couple</strong><br>
                        <a href="https://videochatcouple.com" style="color: #667eea;">videochatcouple.com</a>
                    </p>
                </div>
            `
        );

        logger.info(`Richiesta supporto da ${userEmail}: ${subject}`);

        res.json({
            success: true,
            message: 'Richiesta di supporto inviata con successo. Riceverai una risposta entro 24 ore.',
            ticketId: supportResult.messageId
        });

    } catch (error) {
        logger.error('Errore invio richiesta supporto:', error);
        res.status(500).json({
            success: false,
            message: 'Errore interno del server. Riprova più tardi.'
        });
    }
});

// Test email di benvenuto (solo per sviluppo)
router.post('/test-welcome', async (req, res) => {
    try {
        const { email } = req.body;
        
        if (!email) {
            return res.status(400).json({
                success: false,
                message: 'Email richiesta'
            });
        }

        const result = await emailService.sendWelcomeEmail(email);
        
        res.json({
            success: result.success,
            message: result.success ? 'Email di test inviata' : 'Errore invio email',
            details: result
        });

    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Errore interno del server'
        });
    }
});

module.exports = router;
