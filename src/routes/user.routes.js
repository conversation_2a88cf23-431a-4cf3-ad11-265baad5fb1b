/**
 * Route per gli utenti
 */
const express = require('express');
const jwt = require('jsonwebtoken');
const User = require('../models/user.model');

const router = express.Router();

// JWT Secret
const JWT_SECRET = process.env.JWT_SECRET;

// Middleware di autenticazione
function authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    if (!token) return res.status(401).json({ error: 'Token di accesso richiesto' });

    jwt.verify(token, JWT_SECRET, (err, user) => {
        if (err) return res.status(403).json({ error: 'Token non valido' });
        req.user = user;
        next();
    });
}

// Profilo utente
router.get('/profile', authenticateToken, async (req, res) => {
    try {
        const user = await User.findById(req.user.userId);
        if (!user) {
            return res.status(404).json({ error: 'Utente non trovato' });
        }

        res.json({
            id: user._id,
            email: user.email,
            freeUser: user.freeUser,
            isOnline: user.isOnline,
            createdAt: user.createdAt,
            lastLogin: user.lastLogin
        });
    } catch (error) {
        res.status(500).json({ error: 'Errore interno del server' });
    }
});

// Statistiche utenti online
router.get('/stats', async (req, res) => {
    try {
        const onlineCount = await User.countDocuments({ isOnline: true });
        const totalUsers = await User.countDocuments();
        const freeUsersRemaining = Math.max(0, 10000 - totalUsers);
        const isServiceFree = totalUsers < 10000;

        res.json({
            onlineUsers: onlineCount,
            totalUsers: totalUsers,
            freeUsersRemaining: freeUsersRemaining,
            isServiceFree: isServiceFree,
            donationEmail: '<EMAIL>'
        });
    } catch (error) {
        res.status(500).json({ error: 'Errore interno del server' });
    }
});

module.exports = router;
