/**
 * Route per la configurazione WebRTC
 */
import express from 'express';
import config from '../config/config.js';
import { authenticate } from '../middleware/auth.middleware.js';

const router = express.Router();

/**
 * @route GET /api/webrtc/config
 * @desc Ottiene la configurazione WebRTC
 * @access Private
 */
router.get('/config', authenticate, (req, res) => {
  try {
    // Invia la configurazione WebRTC al client
    res.json({
      iceServers: config.webrtc.iceServers,
      iceCandidatePoolSize: config.webrtc.iceCandidatePoolSize,
      bundlePolicy: config.webrtc.bundlePolicy,
      rtcpMuxPolicy: config.webrtc.rtcpMuxPolicy,
      sdpSemantics: config.webrtc.sdpSemantics
    });
  } catch (error) {
    res.status(500).json({ message: 'Errore durante il recupero della configurazione WebRTC' });
  }
});

export default router;
