/**
 * Middleware per l'autenticazione - Versione semplificata
 */
const jwt = require('jsonwebtoken');
const User = require('../models/user.model');
const logger = require('../utils/logger');

/**
 * Middleware per verificare il token JWT
 */
const authenticateToken = async (req, res, next) => {
  try {
    // Ottieni il token dall'header Authorization
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: 'Token di autenticazione richiesto'
      });
    }

    const token = authHeader.split(' ')[1];
    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Token non fornito'
      });
    }

    try {
      // Verifica il token
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'videochat-secret-key');

      // Trova l'utente
      const user = await User.findById(decoded.id);
      if (!user) {
        return res.status(401).json({
          success: false,
          message: 'Utente non trovato'
        });
      }

      // Aggiungi l'utente alla richiesta
      req.user = {
        id: user._id,
        email: user.email,
        isAdmin: user.isAdmin || false
      };

      next();
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        return res.status(401).json({
          success: false,
          message: 'Token scaduto'
        });
      }
      if (error.name === 'JsonWebTokenError') {
        return res.status(401).json({
          success: false,
          message: 'Token non valido'
        });
      }
      throw error;
    }
  } catch (error) {
    logger.error('Errore autenticazione:', error);
    res.status(500).json({
      success: false,
      message: 'Errore del server durante l\'autenticazione'
    });
  }
};

module.exports = {
  authenticateToken
};
