/**
 * Middleware per l'autenticazione
 */
import jwt from 'jsonwebtoken';
import User from '../models/user.model.js';
import config from '../config/config.js';
import logger from '../config/logging/logger.js';

// Logger specifico per il modulo auth
const authLogger = logger.getModuleLogger('auth');

/**
 * Middleware per verificare il token JWT
 * @param {Object} req - Richiesta Express
 * @param {Object} res - Risposta Express
 * @param {Function} next - Funzione next di Express
 */
const authenticate = async (req, res, next) => {
  try {
    // Ottieni il token dall'header Authorization
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ message: 'Autenticazione richiesta' });
    }

    const token = authHeader.split(' ')[1];
    if (!token) {
      return res.status(401).json({ message: 'Token non fornito' });
    }

    try {
      // Verifica il token
      const decoded = jwt.verify(token, config.auth.jwtSecret);

      // Trova l'utente
      const user = await User.findById(decoded.id);
      if (!user) {
        return res.status(401).json({ message: 'Utente non trovato' });
      }

      // Verifica lo stato dell'utente
      if (user.status !== 'active') {
        return res.status(403).json({ message: 'Account non attivo' });
      }

      // Aggiungi l'utente alla richiesta
      req.user = user;
      req.userId = user._id;
      req.userEmail = user.email;

      next();
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        return res.status(401).json({ message: 'Token scaduto' });
      }
      if (error.name === 'JsonWebTokenError') {
        return res.status(401).json({ message: 'Token non valido' });
      }
      throw error;
    }
  } catch (error) {
    authLogger.error('Errore autenticazione:', error);
    res.status(500).json({ message: 'Errore del server durante l\'autenticazione' });
  }
};

/**
 * Middleware per verificare se l'utente è un amministratore
 * @param {Object} req - Richiesta Express
 * @param {Object} res - Risposta Express
 * @param {Function} next - Funzione next di Express
 */
const isAdmin = (req, res, next) => {
  if (!req.user || !req.user.roles || !req.user.roles.includes('admin')) {
    return res.status(403).json({ message: 'Accesso negato: richiesti privilegi di amministratore' });
  }
  next();
};

/**
 * Middleware per verificare se l'utente è premium
 * @param {Object} req - Richiesta Express
 * @param {Object} res - Risposta Express
 * @param {Function} next - Funzione next di Express
 */
const isPremium = (req, res, next) => {
  if (!req.user || !req.user.premium) {
    return res.status(403).json({ message: 'Accesso negato: richiesto account premium' });
  }
  next();
};

/**
 * Middleware per verificare se l'utente è verificato
 * @param {Object} req - Richiesta Express
 * @param {Object} res - Risposta Express
 * @param {Function} next - Funzione next di Express
 */
const isVerified = (req, res, next) => {
  if (!req.user || !req.user.verified) {
    return res.status(403).json({ message: 'Accesso negato: account non verificato' });
  }
  next();
};

/**
 * Middleware per verificare se l'utente ha crediti sufficienti
 * @param {number} amount - Quantità di crediti richiesti
 * @returns {Function} Middleware Express
 */
const hasCredits = (amount) => {
  return (req, res, next) => {
    if (!req.user || req.user.credits < amount) {
      return res.status(402).json({ message: 'Crediti insufficienti' });
    }
    next();
  };
};

/**
 * Middleware opzionale che verifica il token se presente
 * @param {Object} req - Richiesta Express
 * @param {Object} res - Risposta Express
 * @param {Function} next - Funzione next di Express
 */
const optionalAuth = async (req, res, next) => {
  try {
    // Ottieni il token dall'header Authorization
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      // Nessun token, continua senza autenticazione
      return next();
    }

    const token = authHeader.split(' ')[1];
    if (!token) {
      // Nessun token, continua senza autenticazione
      return next();
    }

    try {
      // Verifica il token
      const decoded = jwt.verify(token, config.auth.jwtSecret);

      // Trova l'utente
      const user = await User.findById(decoded.id);
      if (user && user.status === 'active') {
        // Aggiungi l'utente alla richiesta
        req.user = user;
        req.userId = user._id;
        req.userEmail = user.email;
      }

      next();
    } catch (error) {
      // Ignora errori di token, continua senza autenticazione
      next();
    }
  } catch (error) {
    authLogger.error('Errore autenticazione opzionale:', error);
    next();
  }
};

export {
  authenticate,
  isAdmin,
  isPremium,
  isVerified,
  hasCredits,
  optionalAuth
};
