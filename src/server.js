/**
 * VideoChat Couple - Server principale
 * Sistema completo con login, registrazione, videochat e gestione crediti
 * Versione organizzata con MongoDB Atlas
 */

require('dotenv').config();
const express = require('express');
const http = require('http');
const https = require('https');
const fs = require('fs');
const socketIo = require('socket.io');
const path = require('path');
const cors = require('cors');
const mongoose = require('mongoose');

// Import configurazioni
const { connectDatabase } = require('./config/database');
const { configureExpress } = require('./config/express');
const logger = require('./config/logger');

// Import routes
const authRoutes = require('./routes/auth.routes');
const userRoutes = require('./routes/user.routes');
const sessionRoutes = require('./routes/session.routes');

// Import models
const User = require('./models/user.model');

const app = express();

// Connessione MongoDB
connectDatabase();

// Configurazione Express
configureExpress(app);

// Configurazione server - HTTP per Nginx proxy
const server = http.createServer(app);
logger.info('🔗 Server HTTP configurato per proxy Nginx');

// Configurazione Socket.IO
const io = socketIo(server, {
  cors: {
    origin: "https://videochatcouple.com",
    methods: ["GET", "POST"],
    credentials: true
  }
});

// Database in memoria per sessioni attive
const activeUsers = new Map();
const chatRooms = new Map();

// JWT Secret
const JWT_SECRET = process.env.JWT_SECRET;

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/sessions', sessionRoutes);

// Route principale per servire l'app
app.get('/dashboard', (req, res) => {
    res.sendFile(path.join(__dirname, '../public', 'dashboard.html'));
});

app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '../public', 'index.html'));
});

// Gestione Socket.IO per videochat
io.on('connection', (socket) => {
  logger.info('Nuovo utente connesso:', socket.id);

  // Autenticazione socket
  socket.on('authenticate', async (token) => {
    try {
      const jwt = require('jsonwebtoken');
      const decoded = jwt.verify(token, JWT_SECRET);
      socket.userId = decoded.userId;
      socket.userEmail = decoded.email;

      const user = await User.findById(decoded.userId);
      if (user) {
        user.isOnline = true;
        await user.save();
        activeUsers.set(socket.id, user);

        socket.emit('authenticated', {
          userId: user._id,
          email: user.email,
          freeUser: user.freeUser
        });

        // Notifica agli altri utenti
        socket.broadcast.emit('user-online', {
          userId: user._id,
          email: user.email
        });
      }
    } catch (error) {
      logger.error('Errore autenticazione socket:', error);
      socket.emit('auth-error', 'Token non valido');
    }
  });

  // Richiesta di ricerca partner
  socket.on('find-match', async (data) => {
    const user = activeUsers.get(socket.id);
    if (!user) {
      socket.emit('videochat-error', 'Utente non autenticato');
      return;
    }

    // Verifica se il servizio è ancora gratuito (fino a 10.000 utenti)
    const totalUsers = await User.countDocuments();
    if (totalUsers >= 10000 && !user.freeUser) {
      socket.emit('videochat-error', 'Servizio a pagamento - Contatta <EMAIL> per donazioni');
      return;
    }

    // Cerca un partner disponibile
    const availableUsers = Array.from(activeUsers.entries())
      .filter(([socketId, u]) => socketId !== socket.id);

    if (availableUsers.length === 0) {
      socket.emit('no-partners', 'Nessun partner disponibile al momento');
      return;
    }

    // Seleziona un partner casuale
    const [partnerSocketId, partnerUser] = availableUsers[Math.floor(Math.random() * availableUsers.length)];

    // Crea una stanza di chat
    const roomId = `room_${Date.now()}`;
    chatRooms.set(roomId, {
      users: [socket.id, partnerSocketId],
      createdAt: new Date()
    });

    // Unisci entrambi gli utenti alla stanza
    socket.join(roomId);
    io.sockets.sockets.get(partnerSocketId)?.join(roomId);

    // Notifica entrambi gli utenti
    socket.emit('videochat-matched', {
      roomId,
      partnerId: partnerUser._id,
      partnerEmail: partnerUser.email
    });

    io.to(partnerSocketId).emit('videochat-matched', {
      roomId,
      partnerId: user._id,
      partnerEmail: user.email
    });

    // Non è più necessario decrementare crediti - servizio gratuito
    logger.info(`Match creato tra ${user.email} e ${partnerUser.email}`);
  });

  // Segnali WebRTC
  socket.on('webrtc-offer', (data) => {
    logger.info('Ricevuto offer per room:', data.roomId);
    socket.to(data.roomId).emit('webrtc-offer', {
      offer: data.offer,
      from: socket.id
    });
  });

  socket.on('webrtc-answer', (data) => {
    logger.info('Ricevuto answer per room:', data.roomId);
    socket.to(data.roomId).emit('webrtc-answer', {
      answer: data.answer,
      from: socket.id
    });
  });

  socket.on('webrtc-ice-candidate', (data) => {
    logger.info('Ricevuto ICE candidate per room:', data.roomId);
    socket.to(data.roomId).emit('webrtc-ice-candidate', {
      candidate: data.candidate,
      from: socket.id
    });
  });

  // Messaggi di chat
  socket.on('chat-message', (data) => {
    socket.to(data.roomId).emit('chat-message', {
      message: data.message,
      from: socket.userId,
      timestamp: new Date()
    });
  });

  // Disconnessione
  socket.on('disconnect', async () => {
    logger.info('Utente disconnesso:', socket.id);

    const user = activeUsers.get(socket.id);
    if (user) {
      user.isOnline = false;
      await user.save();
      activeUsers.delete(socket.id);

      // Notifica agli altri utenti
      socket.broadcast.emit('user-offline', {
        userId: user._id,
        email: user.email
      });
    }

    // Rimuovi dalle stanze di chat
    for (const [roomId, room] of chatRooms.entries()) {
      if (room.users.includes(socket.id)) {
        const otherUser = room.users.find(id => id !== socket.id);
        if (otherUser) {
          io.to(otherUser).emit('partner-disconnected');
        }
        chatRooms.delete(roomId);
      }
    }
  });
});

// Catch-all route per SPA
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, '../public', 'index.html'));
});

// Avvio del server
const PORT = process.env.PORT || 3001;

server.listen(PORT, '0.0.0.0', () => {
  logger.info(`🚀 VideoChat Couple server avviato sulla porta ${PORT}`);
  logger.info(`🌐 Sito accessibile su: https://videochatcouple.com`);
  logger.info(`🔒 Sicurezza: Password hashate con bcrypt`);
  logger.info(`💾 Database: MongoDB Atlas connesso`);
  logger.info(`🎯 Pronto per il lancio!`);
  logger.info('🔗 Server HTTP interno per proxy Nginx');
  logger.info('✅ HTTPS gestito da Nginx con certificati SSL');
});

module.exports = { app, server, io };

