import express from 'express';
import mongoose from 'mongoose';
import redis from '../../config/redis.js';
import os from 'os';
import fs from 'fs';
import path from 'path';
import { createRequire } from 'module';

const require = createRequire(import.meta.url);
const { version } = require('../../../package.json');

const router = express.Router();

/**
 * @api {get} /health Verifica lo stato dell'applicazione
 * @apiName GetHealth
 * @apiGroup Health
 * @apiDescription Verifica lo stato dell'applicazione e dei servizi dipendenti
 * @apiSuccess {String} status Stato dell'applicazione
 * @apiSuccess {Object} services Stato dei servizi dipendenti
 * @apiSuccess {Object} system Informazioni sul sistema
 * @apiSuccess {Object} memory Informazioni sulla memoria
 * @apiSuccess {Object} uptime Informazioni sul tempo di attività
 * @apiSuccess {Object} version Informazioni sulla versione
 */
router.get('/', async (req, res) => {
  try {
    // Verifica lo stato di MongoDB
    let mongoStatus = 'error';
    let mongoError = null;
    try {
      const mongoState = mongoose.connection.readyState;
      mongoStatus = mongoState === 1 ? 'ok' : 'error';
      if (mongoState !== 1) {
        mongoError = `MongoDB connection state: ${mongoState}`;
      }
    } catch (err) {
      mongoError = err.message;
    }

    // Verifica lo stato di Redis
    let redisStatus = 'error';
    let redisError = null;
    try {
      if (redis.status === 'ready') {
        redisStatus = 'ok';
      } else {
        redisError = `Redis connection state: ${redis.status}`;
      }
    } catch (err) {
      redisError = err.message;
    }

    // Verifica lo stato del file system
    let fsStatus = 'error';
    let fsError = null;
    try {
      const testFile = path.join(os.tmpdir(), 'health-check-test.txt');
      fs.writeFileSync(testFile, 'test');
      fs.unlinkSync(testFile);
      fsStatus = 'ok';
    } catch (err) {
      fsError = err.message;
    }

    // Informazioni sul sistema
    const systemInfo = {
      platform: os.platform(),
      arch: os.arch(),
      release: os.release(),
      hostname: os.hostname(),
      cpus: os.cpus().length,
      loadAvg: os.loadavg()
    };

    // Informazioni sulla memoria
    const totalMem = os.totalmem();
    const freeMem = os.freemem();
    const usedMem = totalMem - freeMem;
    const memoryInfo = {
      total: formatBytes(totalMem),
      free: formatBytes(freeMem),
      used: formatBytes(usedMem),
      usagePercentage: Math.round((usedMem / totalMem) * 100)
    };

    // Informazioni sul tempo di attività
    const uptimeInfo = {
      server: formatUptime(os.uptime()),
      process: formatUptime(process.uptime())
    };

    // Informazioni sulla versione
    const versionInfo = {
      app: version,
      node: process.version,
      dependencies: {
        express: require('express/package.json').version,
        mongoose: require('mongoose/package.json').version
      }
    };

    // Determina lo stato generale dell'applicazione
    const overallStatus = mongoStatus === 'ok' && redisStatus === 'ok' && fsStatus === 'ok'
      ? 'ok'
      : 'degraded';

    // Costruisci la risposta
    const response = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      services: {
        mongodb: {
          status: mongoStatus,
          error: mongoError
        },
        redis: {
          status: redisStatus,
          error: redisError
        },
        filesystem: {
          status: fsStatus,
          error: fsError
        }
      },
      system: systemInfo,
      memory: memoryInfo,
      uptime: uptimeInfo,
      version: versionInfo
    };

    // Imposta lo status code appropriato
    const statusCode = overallStatus === 'ok' ? 200 : 503;

    res.status(statusCode).json(response);
  } catch (error) {
    res.status(500).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
});

/**
 * @api {get} /health/ping Ping semplice per verificare che l'applicazione risponda
 * @apiName GetHealthPing
 * @apiGroup Health
 * @apiDescription Semplice endpoint per verificare che l'applicazione risponda
 * @apiSuccess {String} status Stato dell'applicazione
 */
router.get('/ping', (req, res) => {
  res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString()
  });
});

/**
 * Formatta i byte in una stringa leggibile
 * @param {Number} bytes Numero di byte
 * @returns {String} Stringa formattata
 */
function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Formatta i secondi in una stringa leggibile
 * @param {Number} seconds Numero di secondi
 * @returns {String} Stringa formattata
 */
function formatUptime(seconds) {
  const days = Math.floor(seconds / (3600 * 24));
  const hours = Math.floor((seconds % (3600 * 24)) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  const parts = [];
  if (days > 0) parts.push(`${days}d`);
  if (hours > 0) parts.push(`${hours}h`);
  if (minutes > 0) parts.push(`${minutes}m`);
  if (secs > 0 || parts.length === 0) parts.push(`${secs}s`);

  return parts.join(' ');
}

export default router;
